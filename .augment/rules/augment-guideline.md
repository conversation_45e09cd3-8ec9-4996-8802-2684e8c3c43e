---
type: "manual"
---

# EchoSync AI Extension - Augment Guidelines

## 项目概述
EchoSync 是一个 AI 提示词同步器，采用 Monorepo 架构，包含 Chrome 插件和官方网站两个主要模块。


## 技术栈

### Monorepo 管理
- **包管理**: npm workspaces (extension + website)
- **代码规范**: ESLint + Prettier + TypeScript
- **CI/CD**: GitHub Actions

### Chrome 插件 (extension/)
- **框架**: React 18 + TypeScript 5
- **构建**: Vite 5 + @crxjs/vite-plugin 2
- **样式**: Tailwind CSS 3 + shadcn/ui
- **状态**: Zustand 4
- **路由**: React Router 6
- **测试**: Jest 29 + React Testing Library

### 官方网站 (website/)
- **框架**: Next.js 14+ (App Router)
- **后端**: Supabase (PostgreSQL + Auth + Storage)
- **支付**: Stripe
- **样式**: Tailwind CSS 3 + shadcn/ui
- **动画**: Framer Motion 10+
- **部署**: Vercel

## 项目结构

### 根目录
```
/
├── extension/          # Chrome 插件
├── website/           # Next.js 官网
├── docs/              # 项目文档
├── package.json       # Monorepo 配置
└── augment.guideline  # 本文件
```

### 插件结构 (extension/src/)
```
src/
├── background/        # Service Worker
├── content/          # Content Scripts
│   └── adapters/     # 各平台适配器
├── popup/            # 弹窗页面
├── options/          # 设置页面
├── components/       # 共享组件
│   └── ui/          # shadcn/ui 组件
├── lib/             # 工具库
├── stores/          # Zustand 状态管理
├── types/           # TypeScript 类型
└── styles/          # 全局样式
```

## 开发规范

要求：
0. 作为一名资深前端和Chrome插件开发程序员，一定要使用最佳实践。
1. 单个ts文件不能超过300行，超过一定要拆分为多个ts文件
2. 一定要遵循最简洁设计模式，不要过度设计
3. 继承不超过2级，再多用组合模式
4. 子类优先选择对父类的扩充，而不是完全覆盖。
5. 尽量不生成测试脚本，本项目暂时不需要单元测试。

### 命令使用
- **开发**: `npm run dev` (同时启动插件和网站)
- **构建**: `npm run build`
- **测试**: `npm run test`
- **代码检查**: `npm run lint`

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 配置
- 组件使用 PascalCase，文件使用 kebab-case
- 优先使用函数组件和 Hooks

### 状态管理
- 插件使用 Zustand 进行状态管理
- 跨页面通信使用 Chrome Extension API
- 数据持久化使用 Chrome Storage API

### 样式规范
- 使用 Tailwind CSS 原子化类名
- UI 组件基于 shadcn/ui + Radix UI
- 保持插件和网站设计风格统一

### 测试要求
- 组件测试使用 React Testing Library
- 单元测试覆盖核心业务逻辑
- 插件功能测试重点关注消息传递和存储

## 文件命名约定
- 组件文件: `ComponentName.tsx`
- 工具文件: `utility-name.ts`
- 类型文件: `types.ts` 或 `index.ts`
- 测试文件: `*.test.ts` 或 `*.spec.ts`

## 重要注意事项
- 插件遵循 Chrome Extension Manifest V3 规范
- 所有跨域请求需在 manifest.json 中声明权限
- 使用 @crxjs/vite-plugin 进行热重载开发
- 网站部署前需配置 Supabase 和 Stripe 环境变量

## 依赖管理
- 根目录管理公共依赖
- 子包独立管理特定依赖
- 使用 npm workspaces 进行依赖提升
- 定期更新依赖版本保持安全性
