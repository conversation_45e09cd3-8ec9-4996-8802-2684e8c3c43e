# EchoAIExtention 项目开发规则

## 1. 代码质量与规范

### 1.1 TypeScript 规范
- 必须使用严格模式 (`"strict": true`)
- 禁止使用 `any` 类型，必须明确类型定义
- 所有函数必须有明确的返回类型声明
- 使用 `interface` 而非 `type` 定义对象类型
- 导出的类型定义必须放在 `types/` 目录下

### 1.2 React 组件规范
- 使用函数式组件和 React Hooks
- 组件名必须使用 PascalCase
- 自定义 Hook 必须以 `use` 开头
- 组件文件使用 `.tsx` 扩展名
- 每个组件必须有对应的类型定义
- 使用 `React.memo` 优化性能关键组件

### 1.3 代码格式化
- 使用 ESLint + Prettier 进行代码检查和格式化
- 提交前必须通过 `npm run lint` 检查
- 使用 2 空格缩进
- 字符串使用双引号
- 行尾不允许有分号（除非必要）

## 2. 项目结构规范

### 2.1 Monorepo 管理
- 使用 npm workspaces 管理子项目
- 共享依赖放在根目录 `package.json`
- 子项目特有依赖放在各自的 `package.json`
- 禁止在子项目间直接引用文件，使用 npm link

### 2.2 目录命名规范
- 使用 kebab-case 命名目录
- 组件目录使用 PascalCase
- 工具函数目录使用 camelCase
- 常量和配置文件使用 UPPER_CASE

### 2.3 文件命名规范
- React 组件文件使用 PascalCase.tsx
- 工具函数文件使用 camelCase.ts
- 类型定义文件使用 camelCase.types.ts
- 测试文件使用 fileName.test.ts

## 3. Chrome 插件开发规范

### 3.1 Manifest V3 规范
- 严格遵循 Manifest V3 标准
- 使用 Service Worker 替代 Background Scripts
- 最小化权限申请，只申请必要权限
- 定期更新 CSP (Content Security Policy)

### 3.2 消息传递规范
- 使用 `chrome.runtime.sendMessage` 进行组件间通信
- 消息类型必须在 `types/messages.ts` 中定义
- 错误处理必须包含 `chrome.runtime.lastError` 检查
- 异步操作使用 Promise 包装

### 3.3 存储规范
- 优先使用 `chrome.storage.sync` 进行跨设备同步
- 大数据使用 `chrome.storage.local`
- 存储键名使用命名空间前缀 `echo_`
- 实现存储配额检查和清理机制

## 4. Next.js 网站开发规范

### 4.1 App Router 规范
- 使用 Next.js 14+ App Router
- 页面组件放在 `app/` 目录下
- API 路由放在 `app/api/` 目录下
- 使用 Server Components 优化性能
- 客户端组件必须明确标记 `'use client'`

### 4.2 数据获取规范
- 使用 `fetch` 进行数据请求
- 实现请求缓存和重新验证策略
- 错误边界必须处理网络错误
- 使用 Suspense 处理加载状态

### 4.3 Supabase 集成规范
- 数据库操作必须使用 RLS (Row Level Security)
- 敏感操作必须在服务端进行
- 使用 TypeScript 类型生成工具
- 实现连接池和错误重试机制

## 5. 状态管理规范

### 5.1 Zustand 使用规范
- Store 文件放在 `stores/` 目录下
- 使用 TypeScript 严格类型定义
- 实现 persist 中间件进行持久化
- 避免在 Store 中存储大量数据

### 5.2 状态更新规范
- 使用 Immer 进行不可变更新
- 避免直接修改状态对象
- 复杂状态更新使用 reducer 模式
- 实现状态变更日志记录

## 6. 测试规范

### 6.1 单元测试
- 使用 Jest + React Testing Library
- 测试覆盖率必须达到 80% 以上
- 每个组件必须有对应测试文件
- 测试文件与源文件放在同一目录

### 6.2 集成测试
- API 路由必须有集成测试
- 使用 Mock 数据进行测试
- 测试环境变量配置独立
- 实现测试数据库隔离

### 6.3 E2E 测试
- 关键用户流程必须有 E2E 测试
- 使用 Playwright 进行浏览器测试
- 测试用例必须可重复执行
- 实现测试报告生成

## 7. 安全规范

### 7.1 数据安全
- 敏感数据必须加密存储
- API 密钥使用环境变量管理
- 禁止在客户端存储敏感信息
- 实现数据脱敏和匿名化

### 7.2 网络安全
- 所有 API 请求使用 HTTPS
- 实现 CSRF 防护
- 使用 Content Security Policy
- 定期更新依赖包安全补丁

### 7.3 用户隐私
- 遵循 GDPR 和相关隐私法规
- 实现用户数据导出和删除
- 最小化数据收集原则
- 透明的隐私政策

## 8. 版本控制规范

### 8.1 Git 工作流
- 使用 Git Flow 分支模型
- 主分支 `main` 保持稳定
- 功能开发使用 `feature/` 分支
- 发布使用 `release/` 分支
- 紧急修复使用 `hotfix/` 分支

### 8.2 提交规范
- 使用 Conventional Commits 格式
- 提交信息必须清晰描述变更
- 每次提交必须是原子性的
- 提交前必须通过所有测试

### 8.3 版本发布
- 使用语义化版本号 (SemVer)
- 每个版本必须有 CHANGELOG
- 发布前必须通过完整测试
- 使用 GitHub Releases 管理版本

## 9. 文档规范

### 9.1 代码文档
- 复杂函数必须有 JSDoc 注释
- API 接口必须有完整文档
- 组件必须有 Props 类型说明
- 配置文件必须有注释说明

### 9.2 项目文档
- README 必须包含快速开始指南
- 架构设计必须有图表说明
- API 文档使用 OpenAPI 规范
- 用户手册必须及时更新

## 10. 性能优化规范

### 10.1 前端性能
- 使用 Code Splitting 减少包大小
- 实现图片懒加载和优化
- 使用 Web Workers 处理重计算
- 监控 Core Web Vitals 指标

### 10.2 后端性能
- 数据库查询必须有索引优化
- 实现 API 响应缓存
- 使用 CDN 加速静态资源
- 监控服务器性能指标

## 11. CI/CD 规范

### 11.1 自动化流程
- 每次 PR 必须通过 CI 检查
- 自动运行测试和代码检查
- 自动构建和部署到测试环境
- 生产部署需要手动审批

### 11.2 部署规范
- 使用蓝绿部署策略
- 实现回滚机制
- 监控部署状态和健康检查
- 记录部署日志和变更

## 12. 监控与日志

### 12.1 错误监控
- 使用 Sentry 进行错误追踪
- 实现用户行为分析
- 监控性能指标
- 设置告警机制

### 12.2 日志规范
- 使用结构化日志格式
- 敏感信息不得记录到日志
- 实现日志轮转和清理
- 提供日志查询和分析工具

---

**注意**: 所有规则都应该在实际开发中严格执行，如有特殊情况需要例外，必须在代码审查中说明原因并获得团队同意。