import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { 
  AppState, 
  User, 
  Prompt, 
  Conversation, 
  Settings, 
  SyncSettings,
  AIPlatform 
} from '@/types'
import { StorageService } from '@/lib/services/storage'

interface AppStore extends AppState {
  // 用户相关
  setUser: (user: User | null) => void
  login: (user: User) => Promise<void>
  logout: () => Promise<void>

  // 同步设置
  updateSyncSettings: (settings: Partial<SyncSettings>) => void
  togglePlatformSync: (platform: AIPlatform) => void

  // 提示词管理
  addPrompt: (prompt: Omit<Prompt, 'id' | 'timestamp'>) => Promise<void>
  removePrompt: (id: string) => void
  togglePromptFavorite: (id: string) => void
  getPromptsByPlatform: (platform: AIPlatform) => Prompt[]
  searchPrompts: (query: string) => Prompt[]

  // 对话管理
  addConversation: (conversation: Omit<Conversation, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  removeConversation: (id: string) => void
  getConversationsByPlatform: (platform: AIPlatform) => Conversation[]

  // 设置管理
  updateSettings: (settings: Partial<Settings>) => Promise<void>
  
  // UI状态
  setActiveTab: (tab: string) => void
  setLoading: (loading: boolean) => void

  // 初始化
  initialize: () => Promise<void>
}

const defaultSyncSettings: SyncSettings = {
  enabled: true,
  platforms: ['chatgpt', 'deepseek', 'claude'],
  autoCapture: true,
  realTimeSync: false
}

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      syncSettings: defaultSyncSettings,
      prompts: [],
      conversations: [],
      settings: {} as Settings,
      isLoading: false,
      activeTab: 'home',

      // 用户相关方法
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      
      login: async (user) => {
        await StorageService.saveUser(user)
        set({ user, isAuthenticated: true })
      },

      logout: async () => {
        await StorageService.removeUser()
        set({ user: null, isAuthenticated: false })
      },

      // 同步设置方法
      updateSyncSettings: (newSettings) => {
        const syncSettings = { ...get().syncSettings, ...newSettings }
        set({ syncSettings })
      },

      togglePlatformSync: (platform) => {
        const { syncSettings } = get()
        const platforms = syncSettings.platforms.includes(platform)
          ? syncSettings.platforms.filter(p => p !== platform)
          : [...syncSettings.platforms, platform]
        
        set({ 
          syncSettings: { ...syncSettings, platforms }
        })
      },

      // 提示词管理方法 - 已迁移到chatHistoryService，这些方法保留用于向后兼容
      addPrompt: async (promptData) => {
        console.warn('addPrompt is deprecated, use chatHistoryService.create instead')
        // 这里可以添加迁移逻辑，但建议直接使用新的服务
      },

      removePrompt: (id) => {
        console.warn('removePrompt is deprecated, use chatHistoryService.delete instead')
        // 保留空实现以避免错误
      },

      togglePromptFavorite: (id) => {
        console.warn('togglePromptFavorite is deprecated, use chatHistoryService.update instead')
        // 保留空实现以避免错误
      },

      getPromptsByPlatform: (platform) => {
        console.warn('getPromptsByPlatform is deprecated, use chatHistoryService.getList with platform filter instead')
        return []
      },

      searchPrompts: (query) => {
        console.warn('searchPrompts is deprecated, use chatHistoryService.search instead')
        return []
      },

      // 对话管理方法
      addConversation: async (conversationData) => {
        const conversation: Conversation = {
          ...conversationData,
          id: Date.now().toString(36) + Math.random().toString(36).substr(2),
          createdAt: Date.now(),
          updatedAt: Date.now()
        }

        const conversations = [conversation, ...get().conversations]
        set({ conversations })
        await StorageService.addConversation(conversation)
      },

      removeConversation: (id) => {
        const conversations = get().conversations.filter(c => c.id !== id)
        set({ conversations })
        StorageService.saveConversations(conversations)
      },

      getConversationsByPlatform: (platform) => {
        return get().conversations.filter(c => c.platform === platform)
      },

      // 设置管理方法
      updateSettings: async (newSettings) => {
        const settings = { ...get().settings, ...newSettings }
        set({ settings })
        await StorageService.saveSettings(settings)
      },

      // UI状态方法
      setActiveTab: (tab) => set({ activeTab: tab }),
      setLoading: (loading) => set({ isLoading: loading }),

      // 初始化方法
      initialize: async () => {
        set({ isLoading: true })
        
        try {
          const [user, conversations, settings] = await Promise.all([
            StorageService.getUser(),
            StorageService.getConversations(),
            StorageService.getSettings()
          ])

          set({
            user,
            isAuthenticated: !!user,
            prompts: [], // 提示词现在通过chatHistoryService管理
            conversations,
            settings,
            isLoading: false
          })
        } catch (error) {
          console.error('Initialize store error:', error)
          set({ isLoading: false })
        }
      }
    }),
    {
      name: 'echosync-store'
    }
  )
)
