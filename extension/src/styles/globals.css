@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Chrome Extension 特定样式 */
.popup-container {
  @apply w-80 h-[600px] overflow-hidden bg-background;
}

.popup-header {
  @apply h-16 border-b bg-background/95 backdrop-blur flex items-center justify-between px-4;
}

.popup-content {
  @apply flex-1 overflow-y-auto p-4;
}

.popup-footer {
  @apply h-12 border-t bg-background/95 flex items-center justify-center px-4;
}

.options-container {
  @apply min-h-screen bg-background;
}

.options-sidebar {
  @apply w-64 border-r bg-muted/50 p-4;
}

.options-main {
  @apply flex-1 p-6;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-muted/50;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提示词卡片样式 */
.prompt-card {
  @apply p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer;
}

.prompt-card:hover {
  @apply shadow-sm;
}

.prompt-card.selected {
  @apply bg-primary/10 border-primary;
}

/* 平台标签样式 */
.platform-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.platform-badge.chatgpt {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.platform-badge.deepseek {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.platform-badge.claude {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300;
}

.platform-badge.gemini {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300;
}
