import React from 'react'

const OptionsApp: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          EchoSync 设置
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">同步设置</h2>
          <p className="text-gray-600">
            在这里配置您的提示词同步偏好设置。
          </p>
          
          <div className="mt-6">
            <label className="flex items-center">
              <input 
                type="checkbox" 
                className="mr-2" 
                defaultChecked 
              />
              <span>自动同步提示词</span>
            </label>
          </div>
          
          <div className="mt-4">
            <label className="flex items-center">
              <input 
                type="checkbox" 
                className="mr-2" 
                defaultChecked 
              />
              <span>保存对话历史</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OptionsApp