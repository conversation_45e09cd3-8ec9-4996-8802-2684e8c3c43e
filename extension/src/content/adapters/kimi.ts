import { AIAdapter } from '../base'
import { Conversation, Message, AIPlatform } from '@/types'

export class <PERSON><PERSON><PERSON><PERSON>pt<PERSON> extends AIAdapter {
  constructor() {
    console.log('【EchoSync】KimiAdapter constructor called')

    const platform = {
      name: '<PERSON><PERSON>',
      id: 'kimi' as AIPlatform,
      url: 'https://kimi.moonshot.cn'
    }

    const selectors = {
      inputField: '[data-lexical-editor="true"], .chat-input-editor[contenteditable="true"], .chat-input-editor, [contenteditable="true"]',
      sendButton: '.send-button-container:not(.disabled) .send-button, .send-button:not(.disabled), .chat-editor-action .send-button-container:not(.disabled) .send-button',
      messageContainer: '.chat-content-item, .segment'
    }

    super(platform, selectors)
    console.log('【EchoSync】KimiAdapter initialized with selectors:', selectors)
  }



  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // Kimi使用特定的类名来区分用户和助手消息
        const isUser = element.classList.contains('chat-content-item-user') ||
                      element.classList.contains('segment-user') ||
                      element.querySelector('.user-content') !== null

        const isAssistant = element.classList.contains('chat-content-item-assistant') ||
                           element.classList.contains('segment-assistant') ||
                           element.querySelector('.markdown-container') !== null

        // 只处理用户或助手消息
        if (!isUser && !isAssistant) return

        const contentElement = element.querySelector('.user-content') ||
                              element.querySelector('.markdown-container .markdown') ||
                              element.querySelector('.segment-content-box') ||
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 获取对话标题
      const titleElement = document.querySelector('.chat-header-content h2, .chat-name')
      const title = titleElement?.textContent?.trim() || `Kimi对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `kimi-${Date.now()}`,
        platform: 'kimi',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract Kimi conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    const hostname = window.location.hostname
    const isValid = hostname.includes('kimi.moonshot.cn') || hostname.includes('kimi.com')
    console.log('【EchoSync】KimiAdapter.isValidPage() - hostname:', hostname, 'isValid:', isValid)
    return isValid
  }

  /**
   * 重写自定义输入获取方法，适配Kimi的Lexical编辑器
   */
  protected customGetCurrentInput(): string | null {
    const inputElement = this.inputManager.getInputElement()
    if (!inputElement) {
      console.log('【EchoSync】Kimi customGetCurrentInput: No input element found')
      return null
    }

    // Kimi使用Lexical编辑器，需要特殊处理
    if (inputElement.hasAttribute('data-lexical-editor')) {
      // 获取纯文本内容，排除<br>标签
      const textContent = inputElement.textContent || ''
      const content = textContent.trim()
      console.log('【EchoSync】Kimi customGetCurrentInput (Lexical):', content)
      return content
    }

    // 对于其他类型的输入元素，返回null使用默认实现
    return null
  }
}
