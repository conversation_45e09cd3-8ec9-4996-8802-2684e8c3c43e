/**
 * Base Adapter 统一入口文件
 * 导出所有拆分后的模块，保持向后兼容性
 */

// 主适配器类
export { AIAdapter } from './AIAdapter'

// 工具类
export { DOMUtils } from './DOMUtils'

// UI组件
export { FloatingBubble } from './FloatingBubble'
export { ArchiveButton } from './ArchiveButton'
export { DragHandler } from './DragHandler'

// 管理类
export { InputManager } from './InputManager'
export { HistoryManager } from './HistoryManager'

// 为了保持向后兼容，重新导出AIAdapter作为默认导出
export { AIAdapter as default } from './AIAdapter'
