/**
 * DOM操作工具类
 * 从base.ts中提取的DOM相关工具方法
 */
export class DOMUtils {
  /**
   * 等待元素出现
   */
  static waitForElement(selector: string, timeout = 5000): Promise<Element | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector)
        if (element) {
          obs.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  /**
   * 模拟用户输入
   */
  static simulateUserInput(element: HTMLElement, text: string): void {
    // 触发focus事件
    element.focus()

    // 设置值
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
      const inputElement = element as HTMLInputElement
      inputElement.value = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
      
      // 触发change事件
      const changeEvent = new Event('change', { bubbles: true })
      element.dispatchEvent(changeEvent)
    } else if (element.contentEditable === 'true') {
      element.textContent = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
    }
  }

  /**
   * 模拟点击
   */
  static simulateClick(element: HTMLElement): void {
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    })
    element.dispatchEvent(clickEvent)
  }

  /**
   * 获取输入框当前内容
   */
  static getCurrentInput(selectors: { inputField: string }): string {
    const inputElement = document.querySelector(selectors.inputField) as HTMLElement
    if (!inputElement) return ''

    if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
      return (inputElement as HTMLInputElement).value
    } else if (inputElement.contentEditable === 'true') {
      return inputElement.textContent || ''
    }

    return ''
  }

  /**
   * 检查是否可以发送消息
   */
  static canSendMessage(selectors: { sendButton: string }): boolean {
    const sendButton = document.querySelector(selectors.sendButton) as HTMLButtonElement
    return sendButton && !sendButton.disabled
  }

  /**
   * 等待页面加载完成
   */
  static waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
        return
      }

      const handleLoad = () => {
        document.removeEventListener('DOMContentLoaded', handleLoad)
        window.removeEventListener('load', handleLoad)
        resolve()
      }

      document.addEventListener('DOMContentLoaded', handleLoad)
      window.addEventListener('load', handleLoad)
    })
  }

  /**
   * 获取通用输入框选择器
   */
  static getUniversalInputSelectors(): string[] {
    return [
      'textarea[placeholder*="输入"]',
      'textarea[placeholder*="请输入"]',
      'textarea[placeholder*="Message"]',
      'textarea[placeholder*="Type"]',
      'textarea[placeholder*="Enter"]',
      'textarea',
      '[contenteditable="true"]',
      'input[type="text"]'
    ]
  }

  /**
   * 检查是否是输入元素
   */
  static isInputElement(element: HTMLElement): boolean {
    if (!element) return false

    const tagName = element.tagName.toLowerCase()
    const isTextarea = tagName === 'textarea'
    const isInput = tagName === 'input' && (element as HTMLInputElement).type === 'text'
    const isContentEditable = element.contentEditable === 'true'

    return isTextarea || isInput || isContentEditable
  }

  /**
   * 检查元素是否可见
   */
  static isVisibleElement(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)
    
    return rect.width > 0 && 
           rect.height > 0 && 
           style.display !== 'none' && 
           style.visibility !== 'hidden' &&
           style.opacity !== '0'
  }

  /**
   * 查找输入框容器
   */
  static findInputContainer(inputElement: HTMLElement): HTMLElement | null {
    let container = inputElement.parentElement
    let attempts = 0
    const maxAttempts = 5

    while (container && attempts < maxAttempts) {
      const style = window.getComputedStyle(container)
      if (style.position === 'relative' || style.position === 'absolute') {
        return container
      }
      container = container.parentElement
      attempts++
    }

    return inputElement.parentElement
  }
}
