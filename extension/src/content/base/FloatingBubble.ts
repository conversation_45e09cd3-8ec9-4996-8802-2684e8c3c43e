import { HistoryBubble } from '@/components/HistoryBubble'

/**
 * 悬浮气泡管理类
 * 从base.ts中提取的悬浮气泡相关方法
 */
export class FloatingBubble {
  private bubble: HTMLElement | null = null
  private historyBubble: HistoryBubble | null = null
  private originalPosition = { top: '20px', right: '20px', left: 'auto' }

  constructor() {
    this.initializeHistoryBubble()
  }

  /**
   * 创建浮动气泡
   */
  createFloatingBubble(): HTMLElement | null {
    if (this.bubble) {
      console.log('Floating bubble already exists, skipping creation')
      return this.bubble
    }

    console.log('Creating floating bubble...')

    this.bubble = document.createElement('div')
    this.bubble.id = 'echosync-floating-bubble'
    this.bubble.innerHTML = `
      <div class="bubble-content">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
    `

    // 恢复保存的位置
    const savedPosition = localStorage.getItem('echosync-bubble-position')
    if (savedPosition) {
      try {
        const position = JSON.parse(savedPosition)
        this.originalPosition = position
      } catch (e) {
        console.warn('Failed to parse saved position:', e)
      }
    }

    // 设置样式
    this.bubble.style.cssText = `
      position: fixed;
      top: ${this.originalPosition.top};
      right: ${this.originalPosition.right};
      left: ${this.originalPosition.left};
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
    `

    // 添加悬停效果和历史提示词显示
    let hoverTimer: number | null = null

    this.bubble.addEventListener('mouseenter', () => {
      if (this.bubble) {
        this.bubble.style.transform = 'scale(1.1)'
        this.bubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)'
      }

      // 延迟显示历史气泡
      hoverTimer = window.setTimeout(() => {
        this.showHistoryBubble()
      }, 500)
    })

    this.bubble.addEventListener('mouseleave', () => {
      if (this.bubble) {
        this.bubble.style.transform = 'scale(1)'
        this.bubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
      }

      // 清除定时器
      if (hoverTimer) {
        clearTimeout(hoverTimer)
        hoverTimer = null
      }

      // 延迟隐藏历史气泡，给用户时间移动到气泡上
      setTimeout(() => {
        if (!this.isMouseOverHistoryBubble()) {
          this.historyBubble?.hide()
        }
      }, 300)
    })

    // 添加点击事件
    this.bubble.addEventListener('click', (e) => {
      // 检查是否是拖拽后的点击
      const dragHandler = (this.bubble as any)?.dragHandler
      if (dragHandler && dragHandler.isDragging()) {
        e.preventDefault()
        return
      }

      // 触发显示存储的提示词事件
      document.dispatchEvent(new CustomEvent('echosync:show-stored-prompts'))
    })

    // 添加右键点击事件用于调试
    this.bubble.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      document.dispatchEvent(new CustomEvent('echosync:debug-features'))
    })

    document.body.appendChild(this.bubble)
    console.log('【EchoSync】Floating bubble created and added to DOM:', this.bubble)

    return this.bubble
  }

  /**
   * 移动气泡到输入框左上方
   */
  moveToInputField(inputElement?: HTMLElement): void {
    if (!this.bubble) {
      console.warn('Floating bubble not found')
      return
    }

    if (!inputElement) {
      console.warn('Input element not provided')
      return
    }

    const rect = inputElement.getBoundingClientRect()
    const bubbleSize = 60
    const margin = 10

    // 计算位置（输入框左上方）
    const left = Math.max(margin, rect.left - bubbleSize - margin)
    const top = Math.max(margin, rect.top - bubbleSize - margin)

    this.bubble.style.left = `${left}px`
    this.bubble.style.top = `${top}px`
    this.bubble.style.right = 'auto'

    this.bubble.style.transform = 'scale(0.9)' // 稍微缩小

    console.log('Bubble moved to input field')
  }

  /**
   * 移动气泡到默认位置（原来的位置）
   */
  moveToDefaultPosition(): void {
    if (!this.bubble) {
      console.warn('Floating bubble not found')
      return
    }

    this.bubble.style.top = this.originalPosition.top
    this.bubble.style.right = this.originalPosition.right
    this.bubble.style.left = this.originalPosition.left

    this.bubble.style.transform = 'scale(1)' // 恢复原始大小

    console.log('Bubble moved to original position')
  }

  /**
   * 边界回弹效果
   */
  snapToBoundary(): void {
    if (!this.bubble) return

    const rect = this.bubble.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const bubbleSize = 60

    let newLeft = rect.left
    let newTop = rect.top

    // 检查边界并调整位置
    if (rect.left < 0) newLeft = 0
    if (rect.right > windowWidth) newLeft = windowWidth - bubbleSize
    if (rect.top < 0) newTop = 0
    if (rect.bottom > windowHeight) newTop = windowHeight - bubbleSize

    // 应用回弹动画
    this.bubble.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    this.bubble.style.left = `${newLeft}px`
    this.bubble.style.top = `${newTop}px`

    // 保存位置
    this.originalPosition = {
      top: `${newTop}px`,
      left: `${newLeft}px`,
      right: 'auto'
    }

    localStorage.setItem('echosync-bubble-position', JSON.stringify(this.originalPosition))
  }

  /**
   * 初始化历史气泡
   */
  private initializeHistoryBubble(): void {
    if (this.historyBubble) return

    this.historyBubble = new HistoryBubble({
      maxItems: 10,
      maxWidth: 320,
      showPlatformIcons: true
    })

    // 监听历史项点击事件
    document.addEventListener('echosync:history-item-click', (event: any) => {
      const { chat } = event.detail
      // 这里需要回调到主类处理
      document.dispatchEvent(new CustomEvent('echosync:handle-history-click', { detail: { chat } }))
    })
  }

  /**
   * 显示历史气泡
   */
  private async showHistoryBubble(): Promise<void> {
    if (!this.historyBubble || !this.bubble) return

    // 触发获取历史数据的事件
    document.dispatchEvent(new CustomEvent('echosync:request-history-data'))
  }

  /**
   * 检查鼠标是否在历史气泡上
   */
  private isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubble) return false
    return this.historyBubble.visible
  }

  /**
   * 获取气泡元素
   */
  getBubble(): HTMLElement | null {
    return this.bubble
  }

  /**
   * 获取历史气泡
   */
  getHistoryBubble(): HistoryBubble | null {
    return this.historyBubble
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.bubble) {
      this.bubble.remove()
      this.bubble = null
    }
    if (this.historyBubble) {
      this.historyBubble.destroy()
      this.historyBubble = null
    }
  }
}
