/**
 * EchoSync Background Script - 主入口文件
 * 负责模块组装和启动，采用模块化架构
 */

import { lifecycleManager } from './lifecycle'
import { messageHandler } from './messageHandler'
import { databaseManager } from './databaseManager'
import { logger, LogLevel } from './utils/logger'
import { errorHandler } from './utils/errorHandler'

// 设置日志级别（开发环境可以设置为DEBUG）
logger.setLogLevel(LogLevel.INFO)

/**
 * 主应用类
 */
class EchoSyncApp {
  private static instance: EchoSyncApp
  private isInitialized = false
  private initPromise: Promise<void> | null = null

  private constructor() {}

  static getInstance(): EchoSyncApp {
    if (!EchoSyncApp.instance) {
      EchoSyncApp.instance = new EchoSyncApp()
    }
    return EchoSyncApp.instance
  }

  /**
   * 初始化应用
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.info('EchoSync already initialized')
      return
    }

    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this.performInitialization()
    await this.initPromise
  }

  /**
   * 执行初始化
   */
  private async performInitialization(): Promise<void> {
    try {
      logger.info('EchoSync Service Worker Script loaded')
      logger.info('Starting EchoSync initialization...')

      // 1. 初始化生命周期管理器
      logger.info('Initializing lifecycle manager...')
      await lifecycleManager.initialize()

      // 2. 初始化消息处理器
      logger.info('Initializing message handler...')
      messageHandler.initialize()

      // 3. 设置其他事件监听器
      this.setupEventListeners()

      // 4. 标记为已初始化
      this.isInitialized = true
      logger.info('EchoSync initialized successfully')

      // 5. 输出初始化状态
      await this.logInitializationStatus()

    } catch (error) {
      logger.error('EchoSync initialization failed', { error })
      throw errorHandler.handle(error as Error, 'LIFECYCLE', 'EchoSyncApp')
    } finally {
      this.initPromise = null
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听标签页更新，检测AI平台
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this))

    // 处理快捷键
    chrome.commands.onCommand.addListener(this.handleCommand.bind(this))

    logger.debug('Event listeners set up')
  }

  /**
   * 处理标签页更新事件
   */
  private async handleTabUpdated(
    tabId: number,
    changeInfo: chrome.tabs.TabChangeInfo,
    tab: chrome.tabs.Tab
  ): Promise<void> {
    if (changeInfo.status === 'complete' && tab.url) {
      const supportedDomains = [
        'chat.openai.com',
        'chat.deepseek.com',
        'claude.ai',
        'gemini.google.com',
        'kimi.moonshot.cn'
      ]

      const isSupported = supportedDomains.some(domain => tab.url && tab.url.includes(domain))

      if (isSupported) {
        logger.debug(`Supported AI platform detected: ${tab.url}`)

        // 注入content script（如果需要）
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            files: ['content/index.js']
          })
          logger.debug(`Content script injected to tab ${tabId}`)
        } catch (error) {
          // 可能已经注入过了，忽略错误
          logger.debug(`Content script injection failed for tab ${tabId}`, { error })
        }
      }
    }
  }

  /**
   * 处理快捷键命令
   */
  private async handleCommand(command: string): Promise<void> {
    logger.info(`Command received: ${command}`)

    try {
      switch (command) {
        case 'open-popup':
          // 打开popup（通过点击图标实现）
          const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
          if (tab.id) {
            chrome.action.openPopup()
          }
          break

        case 'quick-sync':
          // 快速同步当前页面的提示词
          const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true })
          if (currentTab.id) {
            // 这里可以发送消息到content script进行快速同步
            logger.info(`Quick sync triggered for tab ${currentTab.id}`)
          }
          break

        default:
          logger.warn(`Unknown command: ${command}`)
      }
    } catch (error) {
      logger.error('Command handling error', { error, command })
    }
  }

  /**
   * 输出初始化状态
   */
  private async logInitializationStatus(): Promise<void> {
    try {
      const dbStats = await databaseManager.getStats()
      const messageStats = messageHandler.getStats()
      const lifecycleStatus = lifecycleManager.getStatus()

      logger.info('EchoSync Status Report', {
        database: {
          platforms: dbStats.platformCount,
          chatHistory: dbStats.chatHistoryCount
        },
        messages: {
          total: messageStats.totalMessages,
          successful: messageStats.successfulMessages,
          failed: messageStats.failedMessages
        },
        lifecycle: {
          initialized: lifecycleStatus.isInitialized,
          version: lifecycleStatus.version,
          startupCount: lifecycleStatus.startupCount
        }
      })
    } catch (error) {
      logger.warn('Failed to get initialization status', { error })
    }
  }

  /**
   * 获取应用状态
   */
  getStatus(): {
    isInitialized: boolean
    version: string
    uptime: number
  } {
    return {
      isInitialized: this.isInitialized,
      version: '1.0.0',
      uptime: Date.now() - (lifecycleManager.getStatus().lastStartTime || Date.now())
    }
  }

  /**
   * 关闭应用
   */
  shutdown(): void {
    logger.info('Shutting down EchoSync...')

    try {
      messageHandler.shutdown()
      lifecycleManager.shutdown()
      databaseManager.close()

      this.isInitialized = false
      logger.info('EchoSync shut down successfully')
    } catch (error) {
      logger.error('Error during shutdown', { error })
    }
  }
}

// 创建应用实例
const app = EchoSyncApp.getInstance()

// 启动应用
app.initialize().catch(error => {
  console.error('【EchoSync】Failed to initialize EchoSync:', error)
})

// 导出应用实例（用于调试或测试）
;(globalThis as any).EchoSyncApp = app
