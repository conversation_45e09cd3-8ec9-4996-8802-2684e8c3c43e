/**
 * 生命周期管理模块
 * 处理扩展安装、更新、启动事件和Service Worker生命周期
 */

import { databaseManager } from './databaseManager'
import { logger } from './utils/logger'
import { errorHandler, ErrorType } from './utils/errorHandler'
import { StorageService } from '../lib/service/storage'

export interface LifecycleStatus {
  isInitialized: boolean
  installReason?: chrome.runtime.OnInstalledReason
  version: string
  lastStartTime?: number
  startupCount: number
}

export class LifecycleManager {
  private static instance: LifecycleManager
  private status: LifecycleStatus = {
    isInitialized: false,
    version: '1.0.0',
    startupCount: 0
  }
  private cleanupInterval: NodeJS.Timeout | null = null

  private constructor() {}

  static getInstance(): LifecycleManager {
    if (!LifecycleManager.instance) {
      LifecycleManager.instance = new LifecycleManager()
    }
    return LifecycleManager.instance
  }

  /**
   * 初始化生命周期管理
   */
  async initialize(): Promise<void> {
    if (this.status.isInitialized) {
      logger.info('Lifecycle manager already initialized', undefined, 'Lifecycle')
      return
    }

    try {
      logger.lifecycle('Initializing lifecycle manager')

      // 设置事件监听器
      this.setupEventListeners()

      // 初始化数据库
      await this.initializeDatabase()

      // 启动清理任务
      this.startCleanupTasks()

      // 更新状态
      this.status.isInitialized = true
      this.status.lastStartTime = Date.now()
      this.status.startupCount++

      logger.lifecycle('Lifecycle manager initialized successfully', {
        version: this.status.version,
        startupCount: this.status.startupCount
      })

    } catch (error) {
      logger.error('Failed to initialize lifecycle manager', { error }, 'Lifecycle')
      throw errorHandler.handle(error as Error, ErrorType.LIFECYCLE, 'Lifecycle')
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 扩展安装/更新事件
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this))

    // Service Worker启动事件
    chrome.runtime.onStartup.addListener(this.handleStartup.bind(this))

    // 扩展挂起事件（如果支持）
    if (chrome.runtime.onSuspend) {
      chrome.runtime.onSuspend.addListener(this.handleSuspend.bind(this))
    }

    // 扩展挂起取消事件（如果支持）
    if (chrome.runtime.onSuspendCanceled) {
      chrome.runtime.onSuspendCanceled.addListener(this.handleSuspendCanceled.bind(this))
    }

    logger.debug('Event listeners set up', undefined, 'Lifecycle')
  }

  /**
   * 处理扩展安装/更新事件
   */
  private async handleInstalled(details: chrome.runtime.InstalledDetails): Promise<void> {
    try {
      this.status.installReason = details.reason
      logger.lifecycle('Extension installed/updated', {
        reason: details.reason,
        previousVersion: details.previousVersion
      })

      switch (details.reason) {
        case 'install':
          await this.handleFirstInstall()
          break
        case 'update':
          await this.handleUpdate(details.previousVersion)
          break
        case 'chrome_update':
          await this.handleChromeUpdate()
          break
        case 'shared_module_update':
          await this.handleSharedModuleUpdate()
          break
      }

    } catch (error) {
      logger.error('Error handling installed event', { error, details }, 'Lifecycle')
    }
  }

  /**
   * 处理首次安装
   */
  private async handleFirstInstall(): Promise<void> {
    logger.lifecycle('Handling first install')

    try {
      // 初始化默认设置
      const settings = await StorageService.getSettings()
      await StorageService.saveSettings(settings)
      logger.info('Default settings initialized', undefined, 'Lifecycle')

      // 打开欢迎页面
      await chrome.tabs.create({
        url: chrome.runtime.getURL('options/index.html?welcome=true')
      })
      logger.info('Welcome page opened', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Error during first install setup', { error }, 'Lifecycle')
    }
  }

  /**
   * 处理扩展更新
   */
  private async handleUpdate(previousVersion?: string): Promise<void> {
    logger.lifecycle('Handling extension update', { previousVersion, currentVersion: this.status.version })

    try {
      // 这里可以添加版本迁移逻辑
      if (previousVersion) {
        await this.performMigration(previousVersion, this.status.version)
      }

      // 可选：显示更新通知
      // await this.showUpdateNotification(previousVersion)

    } catch (error) {
      logger.error('Error during update handling', { error, previousVersion }, 'Lifecycle')
    }
  }

  /**
   * 处理Chrome更新
   */
  private async handleChromeUpdate(): Promise<void> {
    logger.lifecycle('Handling Chrome update')
    // Chrome更新后可能需要重新初始化某些功能
    await this.reinitializeAfterChromeUpdate()
  }

  /**
   * 处理共享模块更新
   */
  private async handleSharedModuleUpdate(): Promise<void> {
    logger.lifecycle('Handling shared module update')
    // 共享模块更新后的处理逻辑
  }

  /**
   * 处理Service Worker启动
   */
  private async handleStartup(): Promise<void> {
    logger.lifecycle('Service Worker started')
    this.status.lastStartTime = Date.now()
    this.status.startupCount++

    try {
      // 确保数据库连接正常
      await databaseManager.initialize()
      logger.info('Database reinitialized on startup', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Error during startup handling', { error }, 'Lifecycle')
    }
  }

  /**
   * 处理Service Worker挂起
   */
  private handleSuspend(): void {
    logger.lifecycle('Service Worker suspending')
    
    try {
      // 清理资源
      this.cleanup()
      logger.info('Cleanup completed before suspend', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Error during suspend handling', { error }, 'Lifecycle')
    }
  }

  /**
   * 处理Service Worker挂起取消
   */
  private handleSuspendCanceled(): void {
    logger.lifecycle('Service Worker suspend canceled')
    
    try {
      // 重新初始化必要的资源
      this.reinitializeAfterSuspendCancel()

    } catch (error) {
      logger.error('Error during suspend cancel handling', { error }, 'Lifecycle')
    }
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      logger.info('Initializing database...', undefined, 'Lifecycle')
      await databaseManager.initialize()
      
      const stats = await databaseManager.getStats()
      logger.info('Database initialized successfully', stats, 'Lifecycle')

    } catch (error) {
      logger.error('Database initialization failed', { error }, 'Lifecycle')
      throw error
    }
  }

  /**
   * 启动清理任务
   */
  private startCleanupTasks(): void {
    // 每24小时执行一次清理
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performCleanup()
      } catch (error) {
        logger.error('Cleanup task failed', { error }, 'Lifecycle')
      }
    }, 24 * 60 * 60 * 1000)

    logger.debug('Cleanup tasks started', undefined, 'Lifecycle')
  }

  /**
   * 执行清理任务
   */
  private async performCleanup(): Promise<void> {
    logger.info('Starting periodic cleanup...', undefined, 'Lifecycle')

    try {
      const db = await databaseManager.getDatabase()
      const oneMonthAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)

      // 获取过期的聊天历史记录
      const oldRecords = await db.chatHistory
        .where('create_time')
        .below(oneMonthAgo)
        .and(record => record.is_delete === 0)
        .toArray()

      // 删除过期记录
      let deletedCount = 0
      for (const record of oldRecords) {
        await db.chatHistory.update(record.id!, { is_delete: 1 })
        deletedCount++
      }

      logger.info('Cleanup completed', { deletedCount }, 'Lifecycle')

    } catch (error) {
      logger.error('Cleanup failed', { error }, 'Lifecycle')
    }
  }

  /**
   * 执行版本迁移
   */
  private async performMigration(fromVersion: string, toVersion: string): Promise<void> {
    logger.info('Performing migration', { fromVersion, toVersion }, 'Lifecycle')

    try {
      // 这里添加具体的迁移逻辑
      // 例如：数据库结构更新、设置迁移等

      logger.info('Migration completed successfully', { fromVersion, toVersion }, 'Lifecycle')

    } catch (error) {
      logger.error('Migration failed', { error, fromVersion, toVersion }, 'Lifecycle')
      throw error
    }
  }

  /**
   * Chrome更新后重新初始化
   */
  private async reinitializeAfterChromeUpdate(): Promise<void> {
    try {
      // 重新初始化数据库连接
      await databaseManager.reconnect()
      logger.info('Reinitialized after Chrome update', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Reinitialization after Chrome update failed', { error }, 'Lifecycle')
    }
  }

  /**
   * 挂起取消后重新初始化
   */
  private async reinitializeAfterSuspendCancel(): Promise<void> {
    try {
      // 重新启动清理任务
      this.startCleanupTasks()
      logger.info('Reinitialized after suspend cancel', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Reinitialization after suspend cancel failed', { error }, 'Lifecycle')
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    try {
      // 停止清理任务
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval)
        this.cleanupInterval = null
      }

      // 关闭数据库连接
      databaseManager.close()

      logger.debug('Resources cleaned up', undefined, 'Lifecycle')

    } catch (error) {
      logger.error('Error during cleanup', { error }, 'Lifecycle')
    }
  }

  /**
   * 获取生命周期状态
   */
  getStatus(): LifecycleStatus {
    return { ...this.status }
  }

  /**
   * 关闭生命周期管理器
   */
  shutdown(): void {
    logger.lifecycle('Shutting down lifecycle manager')
    this.cleanup()
    this.status.isInitialized = false
  }
}

// 导出单例实例
export const lifecycleManager = LifecycleManager.getInstance()
