import { DatabaseManager } from './databaseManager'

/**
 * 扩展生命周期管理器
 */
export class LifecycleManager {
  private databaseManager: DatabaseManager

  constructor() {
    this.databaseManager = DatabaseManager.getInstance()
  }

  /**
   * 处理扩展安装事件
   */
  async handleInstall(): Promise<void> {
    console.log('【EchoSync】Extension installing...')
    
    try {
      // 初始化数据库
      await this.databaseManager.initialize()
      console.log('【EchoSync】Extension installed successfully')
    } catch (error) {
      console.error('【EchoSync】Extension installation failed:', error)
    }
  }

  /**
   * 处理扩展启动事件
   */
  async handleStartup(): Promise<void> {
    console.log('【EchoSync】Extension starting up...')
    
    try {
      // 确保数据库已初始化
      if (!this.databaseManager.isReady()) {
        await this.databaseManager.initialize()
      }
      console.log('【EchoSync】Extension startup completed')
    } catch (error) {
      console.error('【EchoSync】Extension startup failed:', error)
    }
  }

  /**
   * 处理扩展更新事件
   */
  async handleUpdate(details: chrome.runtime.InstalledDetails): Promise<void> {
    console.log('【EchoSync】Extension updating...', details)
    
    try {
      // 重新初始化数据库
      await this.databaseManager.initialize()
      console.log('【EchoSync】Extension updated successfully')
    } catch (error) {
      console.error('【EchoSync】Extension update failed:', error)
    }
  }

  /**
   * 处理Service Worker激活事件
   */
  async handleActivate(): Promise<void> {
    console.log('【EchoSync】Service Worker activating...')
    
    try {
      // 确保数据库连接
      await this.databaseManager.initialize()
      console.log('【EchoSync】Service Worker activated')
    } catch (error) {
      console.error('【EchoSync】Service Worker activation failed:', error)
    }
  }
}