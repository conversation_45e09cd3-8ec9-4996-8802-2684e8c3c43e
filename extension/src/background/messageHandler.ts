/**
 * 消息处理路由器
 * 统一处理所有消息类型，路由到对应的处理器
 */

import { chatHistoryHandler } from './handlers/chatHistoryHandler'
import { platformHandler } from './handlers/platformHandler'
import { syncHandler } from './handlers/syncHandler'
import { logger } from './utils/logger'
import { handleError, ErrorType } from './utils/errorHandler'
import { MessageType, ChromeMessage } from '../types'

export interface MessageHandlerStats {
  totalMessages: number
  successfulMessages: number
  failedMessages: number
  messagesByType: Record<string, number>
  lastMessageTime?: number
}

export class MessageHandler {
  private static instance: MessageHandler
  private stats: MessageHandlerStats = {
    totalMessages: 0,
    successfulMessages: 0,
    failedMessages: 0,
    messagesByType: {}
  }

  private constructor() {}

  static getInstance(): MessageHandler {
    if (!MessageHandler.instance) {
      MessageHandler.instance = new MessageHandler()
    }
    return MessageHandler.instance
  }

  /**
   * 初始化消息处理器
   */
  initialize(): void {
    logger.info('Initializing message handler...', undefined, 'MessageHandler')
    
    // 设置消息监听器
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this))
    
    logger.info('Message handler initialized successfully', undefined, 'MessageHandler')
  }

  /**
   * 处理消息
   */
  private async handleMessage(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<boolean> {
    // 更新统计信息
    this.updateStats(message.type)

    logger.message(message.type, 'received', {
      payload: message.payload,
      sender: {
        tab: sender.tab?.id,
        url: sender.tab?.url,
        origin: sender.origin
      }
    })

    try {
      // 路由到对应的处理器
      const handled = await this.routeMessage(message, sender, sendResponse)
      
      if (handled) {
        this.stats.successfulMessages++
        logger.debug(`Message ${message.type} handled successfully`, undefined, 'MessageHandler')
      } else {
        this.stats.failedMessages++
        logger.warn(`No handler found for message type: ${message.type}`, undefined, 'MessageHandler')
        sendResponse(handleError.message(`Unknown message type: ${message.type}`, message.type))
      }

      return true // 保持消息通道开放以支持异步响应

    } catch (error) {
      this.stats.failedMessages++
      logger.error('Message handling error', {
        error,
        messageType: message.type,
        payload: message.payload
      }, 'MessageHandler')

      const errorResponse = handleError.message(
        error as Error,
        message.type,
        { payload: message.payload, sender: sender.origin }
      )
      sendResponse(errorResponse)

      return true
    }
  }

  /**
   * 路由消息到对应的处理器
   */
  private async routeMessage(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<boolean> {
    // 聊天历史相关消息
    if (chatHistoryHandler.canHandle(message.type)) {
      await chatHistoryHandler.handleMessage(message, sender, sendResponse)
      return true
    }

    // 平台相关消息
    if (platformHandler.canHandle(message.type)) {
      await platformHandler.handleMessage(message, sender, sendResponse)
      return true
    }

    // 同步相关消息
    if (syncHandler.canHandle(message.type)) {
      await syncHandler.handleMessage(message, sender, sendResponse)
      return true
    }

    // 未找到对应的处理器
    return false
  }

  /**
   * 更新统计信息
   */
  private updateStats(messageType: MessageType): void {
    this.stats.totalMessages++
    this.stats.lastMessageTime = Date.now()
    
    if (!this.stats.messagesByType[messageType]) {
      this.stats.messagesByType[messageType] = 0
    }
    this.stats.messagesByType[messageType]++
  }

  /**
   * 获取统计信息
   */
  getStats(): MessageHandlerStats {
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalMessages: 0,
      successfulMessages: 0,
      failedMessages: 0,
      messagesByType: {},
      lastMessageTime: undefined
    }
    logger.info('Message handler stats reset', undefined, 'MessageHandler')
  }

  /**
   * 获取处理器健康状态
   */
  getHealthStatus(): {
    isHealthy: boolean
    successRate: number
    totalMessages: number
    lastMessageTime?: number
    issues: string[]
  } {
    const issues: string[] = []
    const successRate = this.stats.totalMessages > 0 
      ? (this.stats.successfulMessages / this.stats.totalMessages) * 100 
      : 100

    // 检查成功率
    if (successRate < 90) {
      issues.push(`Low success rate: ${successRate.toFixed(2)}%`)
    }

    // 检查是否有消息处理
    if (this.stats.totalMessages === 0) {
      issues.push('No messages processed yet')
    }

    // 检查最近是否有消息
    const now = Date.now()
    const fiveMinutesAgo = now - (5 * 60 * 1000)
    if (this.stats.lastMessageTime && this.stats.lastMessageTime < fiveMinutesAgo) {
      issues.push('No recent message activity')
    }

    return {
      isHealthy: issues.length === 0,
      successRate,
      totalMessages: this.stats.totalMessages,
      lastMessageTime: this.stats.lastMessageTime,
      issues
    }
  }

  /**
   * 导出统计信息
   */
  exportStats(): string {
    const healthStatus = this.getHealthStatus()
    
    return JSON.stringify({
      stats: this.stats,
      health: healthStatus,
      timestamp: Date.now()
    }, null, 2)
  }

  /**
   * 处理特殊消息类型（如果需要）
   */
  async handleSpecialMessage(
    messageType: string,
    payload: any,
    sender?: chrome.runtime.MessageSender
  ): Promise<any> {
    logger.info(`Handling special message: ${messageType}`, payload, 'MessageHandler')

    try {
      switch (messageType) {
        case 'GET_HANDLER_STATS':
          return this.getStats()
        
        case 'GET_HANDLER_HEALTH':
          return this.getHealthStatus()
        
        case 'RESET_HANDLER_STATS':
          this.resetStats()
          return { success: true }
        
        case 'EXPORT_HANDLER_STATS':
          return { success: true, data: this.exportStats() }
        
        default:
          throw new Error(`Unknown special message type: ${messageType}`)
      }
    } catch (error) {
      logger.error('Special message handling error', { error, messageType }, 'MessageHandler')
      throw error
    }
  }

  /**
   * 关闭消息处理器
   */
  shutdown(): void {
    logger.info('Shutting down message handler...', undefined, 'MessageHandler')
    
    // 这里可以添加清理逻辑，如果需要的话
    // Chrome扩展的消息监听器会自动清理
    
    logger.info('Message handler shut down', undefined, 'MessageHandler')
  }
}

// 导出单例实例
export const messageHandler = MessageHandler.getInstance()
