/**
 * 聊天历史消息处理器
 * 处理所有与聊天历史相关的消息
 */

import { chatHistoryService } from '../../lib/service/chatHistoryDexie'
import { logger } from '../utils/logger'
import { handleError } from '../utils/errorHandler'
import { MessageType, ChromeMessage } from '../../types'

export class ChatHistoryHandler {
  private static instance: ChatHistoryHandler

  private constructor() {}

  static getInstance(): ChatHistoryHandler {
    if (!ChatHistoryHandler.instance) {
      ChatHistoryHandler.instance = new ChatHistoryHandler()
    }
    return ChatHistoryHandler.instance
  }

  /**
   * 处理聊天历史相关消息
   */
  async handleMessage(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    logger.message(message.type, 'received', message.payload)

    try {
      switch (message.type) {
        case MessageType.DB_CHAT_HISTORY_CREATE:
          await this.handleCreate(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_GET_LIST:
          await this.handleGetList(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_GET_UNIQUE:
          await this.handleGetUnique(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_SEARCH:
          await this.handleSearch(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_UPDATE:
          await this.handleUpdate(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_DELETE:
          await this.handleDelete(message, sendResponse)
          break

        case MessageType.DB_CHAT_HISTORY_GET_BY_UID:
          await this.handleGetByUid(message, sendResponse)
          break

        case MessageType.GET_HISTORY:
          await this.handleGetHistory(message, sendResponse)
          break

        default:
          sendResponse(handleError.message('Unknown chat history message type', message.type))
      }
    } catch (error) {
      logger.error('Chat history handler error', { error, messageType: message.type }, 'ChatHistoryHandler')
      sendResponse(handleError.message(error as Error, message.type))
    }
  }

  /**
   * 处理创建聊天历史
   */
  private async handleCreate(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      logger.info('Creating chat history record', message.payload, 'ChatHistoryHandler')
      const result = await chatHistoryService.create(message.payload)
      
      if (result.success) {
        logger.info('Chat history created successfully', { id: result.data?.id }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history creation failed', { error: result.error }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'create', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理获取聊天历史列表
   */
  private async handleGetList(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      logger.debug('Getting chat history list', message.payload, 'ChatHistoryHandler')
      const result = await chatHistoryService.getList(message.payload)
      
      if (result.success) {
        logger.debug('Chat history list retrieved', { count: result.data?.data.length }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history list retrieval failed', { error: result.error }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'getList', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理获取去重聊天历史
   */
  private async handleGetUnique(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      logger.debug('Getting unique chat history', message.payload, 'ChatHistoryHandler')
      const result = await chatHistoryService.getUniqueChats(message.payload)
      
      if (result.success) {
        logger.debug('Unique chat history retrieved', { count: result.data?.length }, 'ChatHistoryHandler')
      } else {
        logger.warn('Unique chat history retrieval failed', { error: result.error }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'getUnique', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理搜索聊天历史
   */
  private async handleSearch(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { searchTerm, params } = message.payload
      logger.debug('Searching chat history', { searchTerm, params }, 'ChatHistoryHandler')
      
      const result = await chatHistoryService.search(searchTerm, params)
      
      if (result.success) {
        logger.debug('Chat history search completed', { count: result.data?.length }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history search failed', { error: result.error }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'search', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理更新聊天历史
   */
  private async handleUpdate(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { id, data } = message.payload
      logger.info('Updating chat history', { id, data }, 'ChatHistoryHandler')
      
      const result = await chatHistoryService.update(id, data)
      
      if (result.success) {
        logger.info('Chat history updated successfully', { id }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history update failed', { error: result.error, id }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'update', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理删除聊天历史
   */
  private async handleDelete(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { id } = message.payload
      logger.info('Deleting chat history', { id }, 'ChatHistoryHandler')
      
      const result = await chatHistoryService.delete(id)
      
      if (result.success) {
        logger.info('Chat history deleted successfully', { id }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history deletion failed', { error: result.error, id }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'delete', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理根据UID获取聊天历史
   */
  private async handleGetByUid(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { chatUid } = message.payload
      logger.debug('Getting chat history by UID', { chatUid }, 'ChatHistoryHandler')
      
      const result = await chatHistoryService.getByChatUid(chatUid)
      
      if (result.success) {
        logger.debug('Chat history by UID retrieved', { chatUid, count: result.data?.length }, 'ChatHistoryHandler')
      } else {
        logger.warn('Chat history by UID retrieval failed', { error: result.error, chatUid }, 'ChatHistoryHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'getByChatUid', 'chatHistory', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理获取历史记录（兼容旧接口）
   */
  private async handleGetHistory(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      logger.debug('Getting history (legacy)', message.payload, 'ChatHistoryHandler')
      const result = await chatHistoryService.getUniqueChats({ limit: 100 })
      
      if (result.success) {
        sendResponse({ success: true, data: result.data })
        logger.debug('History retrieved (legacy)', { count: result.data?.length }, 'ChatHistoryHandler')
      } else {
        sendResponse({ success: false, error: result.error })
        logger.warn('History retrieval failed (legacy)', { error: result.error }, 'ChatHistoryHandler')
      }
    } catch (error) {
      const errorResponse = { success: false, error: 'Failed to get history' }
      sendResponse(errorResponse)
      logger.error('History retrieval error (legacy)', { error }, 'ChatHistoryHandler')
    }
  }

  /**
   * 检查是否可以处理该消息类型
   */
  canHandle(messageType: MessageType): boolean {
    const handledTypes = [
      MessageType.DB_CHAT_HISTORY_CREATE,
      MessageType.DB_CHAT_HISTORY_GET_LIST,
      MessageType.DB_CHAT_HISTORY_GET_UNIQUE,
      MessageType.DB_CHAT_HISTORY_SEARCH,
      MessageType.DB_CHAT_HISTORY_UPDATE,
      MessageType.DB_CHAT_HISTORY_DELETE,
      MessageType.DB_CHAT_HISTORY_GET_BY_UID,
      MessageType.GET_HISTORY
    ]
    
    return handledTypes.includes(messageType)
  }
}

// 导出单例实例
export const chatHistoryHandler = ChatHistoryHandler.getInstance()
