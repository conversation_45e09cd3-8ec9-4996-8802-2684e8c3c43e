/**
 * 同步消息处理器
 * 处理所有与同步相关的消息
 */

import { chatHistoryService } from '../../lib/service/chatHistoryDexie'
import { platformService } from '../../lib/service/platformDexie'
import { StorageService } from '../../lib/service/storage'
import { MessagingService } from '../../lib/service/messaging'
import { logger } from '../utils/logger'
import { handleError } from '../utils/errorHandler'
import { MessageType, ChromeMessage } from '../../types'

export class SyncHandler {
  private static instance: SyncHandler

  private constructor() {}

  static getInstance(): SyncHandler {
    if (!SyncHandler.instance) {
      SyncHandler.instance = new SyncHandler()
    }
    return SyncHandler.instance
  }

  /**
   * 处理同步相关消息
   */
  async handleMessage(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    logger.message(message.type, 'received', message.payload)

    try {
      switch (message.type) {
        case MessageType.SYNC_PROMPT:
          await this.handleSyncPrompt(message, sender, sendResponse)
          break

        case MessageType.CAPTURE_PROMPT:
          await this.handleCapturePrompt(message, sender, sendResponse)
          break

        case MessageType.SAVE_CONVERSATION:
          await this.handleSaveConversation(message, sendResponse)
          break

        case MessageType.UPDATE_SETTINGS:
          await this.handleUpdateSettings(message, sendResponse)
          break

        default:
          sendResponse(handleError.message('Unknown sync message type', message.type))
      }
    } catch (error) {
      logger.error('Sync handler error', { error, messageType: message.type }, 'SyncHandler')
      sendResponse(handleError.sync(error as Error, message.type))
    }
  }

  /**
   * 处理提示词同步
   */
  private async handleSyncPrompt(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const promptData = message.payload
      logger.sync('prompt sync started', promptData.platform)

      // 根据平台名称获取平台信息
      let platform = null
      if (promptData.platform) {
        // 将平台名称标准化（首字母大写）
        const platformName = promptData.platform.charAt(0).toUpperCase() + promptData.platform.slice(1).toLowerCase()
        const platformResult = await platformService.getByName(platformName)

        if (platformResult.success) {
          platform = platformResult.data
        } else {
          logger.warn(`Platform ${platformName} not found, trying to create it`, undefined, 'SyncHandler')
          // 如果平台不存在，尝试创建
          platform = await this.createPlatformIfNotExists(platformName)
        }
      }

      if (!platform) {
        const error = `Failed to get or create platform for: ${promptData.platform}`
        logger.error(error, undefined, 'SyncHandler')
        sendResponse(handleError.sync(error, 'sync_prompt', promptData.platform))
        return
      }

      // 使用chatHistoryService保存提示词
      const result = await chatHistoryService.create({
        chat_prompt: promptData.content,
        platform_id: platform.id!,
        chat_uid: Date.now().toString(),
        create_time: promptData.timestamp || Date.now()
      })

      if (result.success) {
        logger.sync('prompt saved to database', promptData.platform, true)
      } else {
        logger.sync('prompt save failed', promptData.platform, false)
        sendResponse(handleError.sync(result.error || 'Failed to save prompt', 'sync_prompt', promptData.platform))
        return
      }

      // 获取设置，检查是否启用实时同步
      const settings = await StorageService.getSettings()
      if (!settings.syncEnabled) {
        sendResponse({ success: true })
        return
      }

      // 获取所有支持的标签页
      const tabs = await chrome.tabs.query({
        url: [
          'https://chat.openai.com/*',
          'https://chat.deepseek.com/*',
          'https://claude.ai/*',
          'https://gemini.google.com/*',
          'https://kimi.moonshot.cn/*'
        ]
      })

      // 向其他标签页发送同步消息
      const syncPromises = tabs
        .filter(tab => tab.id !== sender.tab?.id) // 排除发送者标签页
        .map(tab => {
          if (tab.id) {
            return MessagingService.sendToContentScript(
              tab.id,
              MessageType.INJECT_PROMPT,
              { prompt: promptData.content }
            ).catch(error => {
              logger.warn(`Failed to sync to tab ${tab.id}`, { error }, 'SyncHandler')
            })
          }
        })

      await Promise.all(syncPromises)
      logger.sync('prompt synced to all tabs', promptData.platform, true)
      sendResponse({ success: true })

    } catch (error) {
      logger.sync('prompt sync failed', message.payload?.platform, false)
      sendResponse(handleError.sync(error as Error, 'sync_prompt', message.payload?.platform))
    }
  }

  /**
   * 处理提示词捕获
   */
  private async handleCapturePrompt(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const data = message.payload
      logger.sync('prompt capture started', data.platform)

      // 根据平台名称获取平台信息
      let platform = null
      if (data.platform) {
        const platformName = data.platform.charAt(0).toUpperCase() + data.platform.slice(1).toLowerCase()
        const platformResult = await platformService.getByName(platformName)

        if (platformResult.success) {
          platform = platformResult.data
        }
      }

      if (!platform) {
        const error = `Failed to get platform for capture: ${data.platform}`
        logger.error(error, undefined, 'SyncHandler')
        sendResponse(handleError.sync(error, 'capture_prompt', data.platform))
        return
      }

      // 使用chatHistoryService保存提示词
      const result = await chatHistoryService.create({
        chat_prompt: data.content,
        platform_id: platform.id!,
        chat_uid: Date.now().toString(),
        create_time: Date.now()
      })

      if (result.success) {
        logger.sync('prompt captured and saved', data.platform, true)

        // 通知popup更新
        try {
          chrome.runtime.sendMessage({
            type: 'PROMPT_CAPTURED',
            payload: {
              id: result.data.id,
              content: data.content,
              platform: data.platform,
              timestamp: Date.now()
            }
          })
        } catch (error) {
          // Popup可能未打开，忽略错误
          logger.debug('Failed to notify popup', { error }, 'SyncHandler')
        }

        sendResponse({ success: true })
      } else {
        logger.sync('prompt capture failed', data.platform, false)
        sendResponse(handleError.sync(result.error || 'Failed to capture prompt', 'capture_prompt', data.platform))
      }

    } catch (error) {
      logger.sync('prompt capture failed', message.payload?.platform, false)
      sendResponse(handleError.sync(error as Error, 'capture_prompt', message.payload?.platform))
    }
  }

  /**
   * 处理保存对话
   */
  private async handleSaveConversation(
    message: ChromeMessage,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      logger.info('Saving conversation', message.payload, 'SyncHandler')
      await StorageService.addConversation(message.payload)
      sendResponse({ success: true })
    } catch (error) {
      sendResponse(handleError.sync(error as Error, 'save_conversation'))
    }
  }

  /**
   * 处理更新设置
   */
  private async handleUpdateSettings(
    message: ChromeMessage,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      logger.info('Updating settings', message.payload, 'SyncHandler')
      await StorageService.saveSettings(message.payload)
      sendResponse({ success: true })
    } catch (error) {
      sendResponse(handleError.sync(error as Error, 'update_settings'))
    }
  }

  /**
   * 创建平台（如果不存在）
   */
  private async createPlatformIfNotExists(platformName: string): Promise<any> {
    const platformUrls: Record<string, string> = {
      'Deepseek': 'https://chat.deepseek.com',
      'Chatgpt': 'https://chat.openai.com',
      'Claude': 'https://claude.ai',
      'Gemini': 'https://gemini.google.com',
      'Kimi': 'https://kimi.moonshot.cn'
    }

    if (platformUrls[platformName]) {
      const createResult = await platformService.create({
        name: platformName,
        url: platformUrls[platformName]
      })

      if (createResult.success) {
        logger.info(`Platform ${platformName} created successfully`, undefined, 'SyncHandler')
        return createResult.data
      } else {
        logger.error(`Failed to create platform ${platformName}`, { error: createResult.error }, 'SyncHandler')
      }
    }

    return null
  }

  /**
   * 检查是否可以处理该消息类型
   */
  canHandle(messageType: MessageType): boolean {
    const handledTypes = [
      MessageType.SYNC_PROMPT,
      MessageType.CAPTURE_PROMPT,
      MessageType.SAVE_CONVERSATION,
      MessageType.UPDATE_SETTINGS
    ]

    return handledTypes.includes(messageType)
  }
}

// 导出单例实例
export const syncHandler = SyncHandler.getInstance()
