/**
 * 平台消息处理器
 * 处理所有与平台相关的消息
 */

import { platformService } from '../../lib/service/platformDexie'
import { logger } from '../utils/logger'
import { handleError } from '../utils/errorHandler'
import { MessageType, ChromeMessage } from '../../types'

export class PlatformHandler {
  private static instance: PlatformHandler

  private constructor() {}

  static getInstance(): PlatformHandler {
    if (!PlatformHandler.instance) {
      PlatformHandler.instance = new PlatformHandler()
    }
    return PlatformHandler.instance
  }

  /**
   * 处理平台相关消息
   */
  async handleMessage(
    message: ChromeMessage,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    logger.message(message.type, 'received', message.payload)

    try {
      switch (message.type) {
        case MessageType.DB_PLATFORM_GET_BY_NAME:
          await this.handleGetByName(message, sendResponse)
          break

        case MessageType.DB_PLATFORM_GET_BY_DOMAIN:
          await this.handleGetByDomain(message, sendResponse)
          break

        case MessageType.DB_PLATFORM_GET_LIST:
          await this.handleGetList(message, sendResponse)
          break

        default:
          sendResponse(handleError.message('Unknown platform message type', message.type))
      }
    } catch (error) {
      logger.error('Platform handler error', { error, messageType: message.type }, 'PlatformHandler')
      sendResponse(handleError.message(error as Error, message.type))
    }
  }

  /**
   * 处理根据名称获取平台
   */
  private async handleGetByName(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { name } = message.payload
      logger.debug('Getting platform by name', { name }, 'PlatformHandler')
      
      const result = await platformService.getByName(name)
      
      if (result.success) {
        logger.debug('Platform retrieved by name', { name, found: !!result.data }, 'PlatformHandler')
      } else {
        logger.warn('Platform retrieval by name failed', { error: result.error, name }, 'PlatformHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'getByName', 'platform', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理根据域名获取平台
   */
  private async handleGetByDomain(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      const { hostname } = message.payload
      logger.debug('Getting platform by domain', { hostname }, 'PlatformHandler')
      
      const result = await platformService.findByDomain(hostname)
      
      if (result.success) {
        logger.debug('Platform retrieved by domain', { hostname, found: !!result.data }, 'PlatformHandler')
      } else {
        logger.warn('Platform retrieval by domain failed', { error: result.error, hostname }, 'PlatformHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'findByDomain', 'platform', message.payload)
      sendResponse(errorResponse)
    }
  }

  /**
   * 处理获取平台列表
   */
  private async handleGetList(message: ChromeMessage, sendResponse: (response?: any) => void): Promise<void> {
    try {
      logger.debug('Getting platform list', undefined, 'PlatformHandler')
      
      const result = await platformService.getAll()
      
      if (result.success) {
        logger.debug('Platform list retrieved', { count: result.data?.length }, 'PlatformHandler')
      } else {
        logger.warn('Platform list retrieval failed', { error: result.error }, 'PlatformHandler')
      }
      
      sendResponse(result)
    } catch (error) {
      const errorResponse = handleError.database(error as Error, 'getAll', 'platform')
      sendResponse(errorResponse)
    }
  }

  /**
   * 检查是否可以处理该消息类型
   */
  canHandle(messageType: MessageType): boolean {
    const handledTypes = [
      MessageType.DB_PLATFORM_GET_BY_NAME,
      MessageType.DB_PLATFORM_GET_BY_DOMAIN,
      MessageType.DB_PLATFORM_GET_LIST
    ]
    
    return handledTypes.includes(messageType)
  }
}

// 导出单例实例
export const platformHandler = PlatformHandler.getInstance()
