/**
 * 数据库管理模块
 * 负责dexie数据库的初始化、连接管理和状态监控
 */

import { dexieDatabase } from '../lib/database/dexie'
import { logger } from './utils/logger'
import { errorHandler, ErrorType } from './utils/errorHandler'

export interface DatabaseStatus {
  isInitialized: boolean
  isConnected: boolean
  lastInitTime?: number
  lastError?: string
  retryCount: number
  maxRetries: number
}

export class DatabaseManager {
  private static instance: DatabaseManager
  private status: DatabaseStatus = {
    isInitialized: false,
    isConnected: false,
    retryCount: 0,
    maxRetries: 3
  }
  private initPromise: Promise<void> | null = null
  private healthCheckInterval: NodeJS.Timeout | null = null

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  /**
   * 获取数据库状态
   */
  getStatus(): DatabaseStatus {
    return { ...this.status }
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    // 如果已经在初始化中，返回现有的Promise
    if (this.initPromise) {
      return this.initPromise
    }

    // 如果已经初始化成功，直接返回
    if (this.status.isInitialized && this.status.isConnected) {
      logger.info('Database already initialized', undefined, 'DatabaseManager')
      return
    }

    logger.info('Starting database initialization...', undefined, 'DatabaseManager')

    this.initPromise = this.performInitialization()
    
    try {
      await this.initPromise
    } finally {
      this.initPromise = null
    }
  }

  /**
   * 执行数据库初始化
   */
  private async performInitialization(): Promise<void> {
    try {
      this.status.retryCount++
      logger.info(`Database initialization attempt ${this.status.retryCount}/${this.status.maxRetries}`, undefined, 'DatabaseManager')

      // 初始化数据库
      await dexieDatabase.initialize()

      // 验证数据库连接
      await this.verifyConnection()

      // 更新状态
      this.status.isInitialized = true
      this.status.isConnected = true
      this.status.lastInitTime = Date.now()
      this.status.lastError = undefined
      this.status.retryCount = 0

      logger.info('Database initialized successfully', {
        platformCount: await dexieDatabase.platform.count(),
        chatHistoryCount: await dexieDatabase.chatHistory.count()
      }, 'DatabaseManager')

      // 启动健康检查
      this.startHealthCheck()

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.status.lastError = errorMessage
      this.status.isInitialized = false
      this.status.isConnected = false

      logger.error('Database initialization failed', {
        error: errorMessage,
        attempt: this.status.retryCount,
        maxRetries: this.status.maxRetries
      }, 'DatabaseManager')

      // 如果还有重试次数，进行重试
      if (this.status.retryCount < this.status.maxRetries) {
        logger.info(`Retrying database initialization in 2 seconds...`, undefined, 'DatabaseManager')
        await new Promise(resolve => setTimeout(resolve, 2000))
        return this.performInitialization()
      } else {
        // 重试次数用完，抛出错误
        throw errorHandler.handle(
          error as Error,
          ErrorType.DATABASE,
          'DatabaseManager',
          { retryCount: this.status.retryCount }
        )
      }
    }
  }

  /**
   * 验证数据库连接
   */
  private async verifyConnection(): Promise<void> {
    try {
      // 尝试执行简单的数据库操作来验证连接
      await dexieDatabase.platform.count()
      await dexieDatabase.chatHistory.count()
      logger.debug('Database connection verified', undefined, 'DatabaseManager')
    } catch (error) {
      throw new Error(`Database connection verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.verifyConnection()
        if (!this.status.isConnected) {
          this.status.isConnected = true
          logger.info('Database connection restored', undefined, 'DatabaseManager')
        }
      } catch (error) {
        if (this.status.isConnected) {
          this.status.isConnected = false
          this.status.lastError = error instanceof Error ? error.message : 'Unknown error'
          logger.warn('Database connection lost', { error: this.status.lastError }, 'DatabaseManager')
        }
      }
    }, 30000) // 每30秒检查一次
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  /**
   * 重新连接数据库
   */
  async reconnect(): Promise<void> {
    logger.info('Attempting to reconnect database...', undefined, 'DatabaseManager')
    
    try {
      // 关闭现有连接
      this.close()
      
      // 重置状态
      this.status.isInitialized = false
      this.status.isConnected = false
      this.status.retryCount = 0
      
      // 重新初始化
      await this.initialize()
      
      logger.info('Database reconnected successfully', undefined, 'DatabaseManager')
    } catch (error) {
      logger.error('Database reconnection failed', { error }, 'DatabaseManager')
      throw error
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    try {
      this.stopHealthCheck()
      dexieDatabase.close()
      
      this.status.isInitialized = false
      this.status.isConnected = false
      
      logger.info('Database connection closed', undefined, 'DatabaseManager')
    } catch (error) {
      logger.error('Error closing database connection', { error }, 'DatabaseManager')
    }
  }

  /**
   * 获取数据库实例（确保已初始化）
   */
  async getDatabase() {
    if (!this.status.isInitialized || !this.status.isConnected) {
      await this.initialize()
    }
    return dexieDatabase
  }

  /**
   * 执行数据库操作（带自动重连）
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string = 'unknown'
  ): Promise<T> {
    try {
      // 确保数据库已初始化
      await this.initialize()
      
      // 执行操作
      const result = await operation()
      logger.database(operationName, 'unknown', undefined, true)
      return result
      
    } catch (error) {
      logger.database(operationName, 'unknown', { error }, false)
      
      // 如果是连接错误，尝试重连
      if (this.isConnectionError(error)) {
        logger.warn(`Database connection error during ${operationName}, attempting reconnect...`, undefined, 'DatabaseManager')
        
        try {
          await this.reconnect()
          const result = await operation()
          logger.database(`${operationName} (retry)`, 'unknown', undefined, true)
          return result
        } catch (retryError) {
          logger.database(`${operationName} (retry)`, 'unknown', { error: retryError }, false)
          throw retryError
        }
      }
      
      throw error
    }
  }

  /**
   * 判断是否为连接错误
   */
  private isConnectionError(error: any): boolean {
    if (!error) return false
    
    const errorMessage = error.message || error.toString()
    const connectionErrorPatterns = [
      'database is closed',
      'connection lost',
      'database not open',
      'transaction failed',
      'database locked'
    ]
    
    return connectionErrorPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern)
    )
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    platformCount: number
    chatHistoryCount: number
    databaseSize?: number
  }> {
    try {
      const db = await this.getDatabase()
      const platformCount = await db.platform.count()
      const chatHistoryCount = await db.chatHistory.count()
      
      return {
        platformCount,
        chatHistoryCount
      }
    } catch (error) {
      logger.error('Failed to get database stats', { error }, 'DatabaseManager')
      return {
        platformCount: 0,
        chatHistoryCount: 0
      }
    }
  }
}

// 导出单例实例
export const databaseManager = DatabaseManager.getInstance()
