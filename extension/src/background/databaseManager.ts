import { EchoSyncDatabase } from '@/lib/database/dexie'
import { ChatHistoryDexieService } from '@/lib/storage/chatHistoryDexie'
import { PlatformDexieService } from '@/lib/storage/platformDexie'

/**
 * 数据库管理器 - 负责数据库的初始化、连接和生命周期管理
 */
export class DatabaseManager {
  private static instance: DatabaseManager
  private database: EchoSyncDatabase | null = null
  private chatHistoryService: ChatHistoryDexieService | null = null
  private platformService: PlatformDexieService | null = null
  private isInitialized = false

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('【EchoSync】Database already initialized')
      return
    }

    try {
      console.log('【EchoSync】Initializing database...')
      
      // 创建数据库实例
      this.database = new EchoSyncDatabase()
      await this.database.open()
      
      // 创建服务实例
      this.chatHistoryService = new ChatHistoryDexieService()
      this.platformService = new PlatformDexieService()
      
      // 初始化默认平台数据
      await this.platformService.initializeDefaultPlatforms()
      
      this.isInitialized = true
      console.log('【EchoSync】Database initialized successfully')
      
    } catch (error) {
      console.error('【EchoSync】Database initialization failed:', error)
      throw error
    }
  }

  /**
   * 获取聊天历史服务
   */
  getChatHistoryService(): ChatHistoryDexieService {
    if (!this.chatHistoryService) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.chatHistoryService
  }

  /**
   * 获取平台服务
   */
  getPlatformService(): PlatformDexieService {
    if (!this.platformService) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.platformService
  }

  /**
   * 检查数据库是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.database) {
      await this.database.close()
      this.database = null
      this.chatHistoryService = null
      this.platformService = null
      this.isInitialized = false
      console.log('【EchoSync】Database closed')
    }
  }
}