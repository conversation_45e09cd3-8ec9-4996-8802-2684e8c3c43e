/**
 * 统一的错误处理工具
 */

import { logger } from './logger'

export enum ErrorType {
  DATABASE = 'DATABASE',
  MESSAGE = 'MESSAGE',
  SYNC = 'SYNC',
  LIFECYCLE = 'LIFECYCLE',
  NETWORK = 'NETWORK',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN'
}

export interface ErrorInfo {
  type: ErrorType
  code?: string
  message: string
  originalError?: Error
  context?: any
  timestamp: number
  module?: string
}

export interface ErrorResponse {
  success: false
  error: string
  code?: string
  details?: any
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private errors: ErrorInfo[] = []
  private maxErrors = 500

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 处理错误
   */
  handle(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN,
    module?: string,
    context?: any,
    code?: string
  ): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type,
      code,
      message: error instanceof Error ? error.message : error,
      originalError: error instanceof Error ? error : undefined,
      context,
      timestamp: Date.now(),
      module
    }

    // 添加到错误历史
    this.errors.push(errorInfo)
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }

    // 记录日志
    logger.error(
      `${type} Error${code ? ` (${code})` : ''}: ${errorInfo.message}`,
      {
        context,
        stack: errorInfo.originalError?.stack
      },
      module
    )

    return errorInfo
  }

  /**
   * 创建错误响应
   */
  createResponse(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN,
    module?: string,
    context?: any,
    code?: string
  ): ErrorResponse {
    const errorInfo = this.handle(error, type, module, context, code)
    
    return {
      success: false,
      error: errorInfo.message,
      code: errorInfo.code,
      details: context
    }
  }

  /**
   * 数据库错误处理
   */
  database(error: Error | string, operation?: string, table?: string, context?: any): ErrorResponse {
    const message = operation && table 
      ? `Database ${operation} on ${table} failed: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.DATABASE,
      'Database',
      { operation, table, ...context },
      'DB_ERROR'
    )
  }

  /**
   * 消息处理错误
   */
  message(error: Error | string, messageType?: string, context?: any): ErrorResponse {
    const message = messageType
      ? `Message handling failed for ${messageType}: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.MESSAGE,
      'Message',
      { messageType, ...context },
      'MSG_ERROR'
    )
  }

  /**
   * 同步错误处理
   */
  sync(error: Error | string, operation?: string, platform?: string, context?: any): ErrorResponse {
    const message = operation && platform
      ? `Sync ${operation} failed for ${platform}: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.SYNC,
      'Sync',
      { operation, platform, ...context },
      'SYNC_ERROR'
    )
  }

  /**
   * 生命周期错误处理
   */
  lifecycle(error: Error | string, event?: string, context?: any): ErrorResponse {
    const message = event
      ? `Lifecycle event ${event} failed: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.LIFECYCLE,
      'Lifecycle',
      { event, ...context },
      'LIFECYCLE_ERROR'
    )
  }

  /**
   * 网络错误处理
   */
  network(error: Error | string, url?: string, context?: any): ErrorResponse {
    const message = url
      ? `Network request to ${url} failed: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.NETWORK,
      'Network',
      { url, ...context },
      'NETWORK_ERROR'
    )
  }

  /**
   * 权限错误处理
   */
  permission(error: Error | string, permission?: string, context?: any): ErrorResponse {
    const message = permission
      ? `Permission ${permission} error: ${error instanceof Error ? error.message : error}`
      : error instanceof Error ? error.message : error

    return this.createResponse(
      message,
      ErrorType.PERMISSION,
      'Permission',
      { permission, ...context },
      'PERMISSION_ERROR'
    )
  }

  /**
   * 获取错误历史
   */
  getErrors(type?: ErrorType, module?: string, limit?: number): ErrorInfo[] {
    let filteredErrors = this.errors

    if (type) {
      filteredErrors = filteredErrors.filter(error => error.type === type)
    }

    if (module) {
      filteredErrors = filteredErrors.filter(error => error.module === module)
    }

    if (limit) {
      filteredErrors = filteredErrors.slice(-limit)
    }

    return filteredErrors
  }

  /**
   * 清除错误历史
   */
  clearErrors(): void {
    this.errors = []
  }

  /**
   * 导出错误历史
   */
  exportErrors(): string {
    return JSON.stringify(this.errors, null, 2)
  }

  /**
   * 检查是否为特定类型的错误
   */
  isErrorType(error: any, type: ErrorType): boolean {
    return error && error.type === type
  }

  /**
   * 安全执行函数，自动处理错误
   */
  async safeExecute<T>(
    fn: () => Promise<T>,
    errorType: ErrorType = ErrorType.UNKNOWN,
    module?: string,
    context?: any
  ): Promise<{ success: true; data: T } | ErrorResponse> {
    try {
      const data = await fn()
      return { success: true, data }
    } catch (error) {
      return this.createResponse(error as Error, errorType, module, context)
    }
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷方法
export const handleError = {
  database: (error: Error | string, operation?: string, table?: string, context?: any) =>
    errorHandler.database(error, operation, table, context),
  message: (error: Error | string, messageType?: string, context?: any) =>
    errorHandler.message(error, messageType, context),
  sync: (error: Error | string, operation?: string, platform?: string, context?: any) =>
    errorHandler.sync(error, operation, platform, context),
  lifecycle: (error: Error | string, event?: string, context?: any) =>
    errorHandler.lifecycle(error, event, context),
  network: (error: Error | string, url?: string, context?: any) =>
    errorHandler.network(error, url, context),
  permission: (error: Error | string, permission?: string, context?: any) =>
    errorHandler.permission(error, permission, context),
  safe: <T>(fn: () => Promise<T>, errorType?: ErrorType, module?: string, context?: any) =>
    errorHandler.safeExecute(fn, errorType, module, context)
}
