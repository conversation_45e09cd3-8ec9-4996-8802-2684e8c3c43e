/**
 * 统一的日志管理工具
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: number
  module?: string
}

export class Logger {
  private static instance: Logger
  private logLevel: LogLevel = LogLevel.INFO
  private prefix = '【EchoSync】'
  private logs: LogEntry[] = []
  private maxLogs = 1000

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  /**
   * 设置日志级别
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }

  /**
   * 设置日志前缀
   */
  setPrefix(prefix: string): void {
    this.prefix = prefix
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, data?: any, module?: string): void {
    if (level < this.logLevel) return

    const logEntry: LogEntry = {
      level,
      message,
      data,
      timestamp: Date.now(),
      module
    }

    // 添加到内存日志
    this.logs.push(logEntry)
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // 输出到控制台
    const formattedMessage = `${this.prefix}${module ? `[${module}] ` : ''}${message}`
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, data || '')
        break
      case LogLevel.INFO:
        console.log(formattedMessage, data || '')
        break
      case LogLevel.WARN:
        console.warn(formattedMessage, data || '')
        break
      case LogLevel.ERROR:
        console.error(formattedMessage, data || '')
        break
    }
  }

  /**
   * Debug级别日志
   */
  debug(message: string, data?: any, module?: string): void {
    this.log(LogLevel.DEBUG, message, data, module)
  }

  /**
   * Info级别日志
   */
  info(message: string, data?: any, module?: string): void {
    this.log(LogLevel.INFO, message, data, module)
  }

  /**
   * Warning级别日志
   */
  warn(message: string, data?: any, module?: string): void {
    this.log(LogLevel.WARN, message, data, module)
  }

  /**
   * Error级别日志
   */
  error(message: string, data?: any, module?: string): void {
    this.log(LogLevel.ERROR, message, data, module)
  }

  /**
   * 获取日志历史
   */
  getLogs(level?: LogLevel, module?: string, limit?: number): LogEntry[] {
    let filteredLogs = this.logs

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level >= level)
    }

    if (module) {
      filteredLogs = filteredLogs.filter(log => log.module === module)
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit)
    }

    return filteredLogs
  }

  /**
   * 清除日志历史
   */
  clearLogs(): void {
    this.logs = []
  }

  /**
   * 导出日志
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  /**
   * 数据库操作日志
   */
  database(operation: string, table: string, data?: any, success: boolean = true): void {
    const message = `Database ${operation} on ${table} ${success ? 'succeeded' : 'failed'}`
    if (success) {
      this.info(message, data, 'Database')
    } else {
      this.error(message, data, 'Database')
    }
  }

  /**
   * 消息处理日志
   */
  message(type: string, direction: 'received' | 'sent', data?: any): void {
    const message = `Message ${type} ${direction}`
    this.info(message, data, 'Message')
  }

  /**
   * 生命周期日志
   */
  lifecycle(event: string, data?: any): void {
    this.info(`Lifecycle event: ${event}`, data, 'Lifecycle')
  }

  /**
   * 同步操作日志
   */
  sync(operation: string, platform?: string, success: boolean = true): void {
    const message = `Sync ${operation}${platform ? ` for ${platform}` : ''} ${success ? 'succeeded' : 'failed'}`
    if (success) {
      this.info(message, undefined, 'Sync')
    } else {
      this.error(message, undefined, 'Sync')
    }
  }
}

// 导出单例实例
export const logger = Logger.getInstance()

// 便捷方法
export const log = {
  debug: (message: string, data?: any, module?: string) => logger.debug(message, data, module),
  info: (message: string, data?: any, module?: string) => logger.info(message, data, module),
  warn: (message: string, data?: any, module?: string) => logger.warn(message, data, module),
  error: (message: string, data?: any, module?: string) => logger.error(message, data, module),
  database: (operation: string, table: string, data?: any, success?: boolean) => 
    logger.database(operation, table, data, success),
  message: (type: string, direction: 'received' | 'sent', data?: any) => 
    logger.message(type, direction, data),
  lifecycle: (event: string, data?: any) => logger.lifecycle(event, data),
  sync: (operation: string, platform?: string, success?: boolean) => 
    logger.sync(operation, platform, success)
}
