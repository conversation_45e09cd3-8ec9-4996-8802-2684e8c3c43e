import { useState, useCallback, useEffect } from 'react'
import { chatHistoryDatabaseProxy } from '../lib/service/databaseProxy'
import {
  ChatHistory,
  CreateChatHistoryInput,
  UpdateChatHistoryInput,
  ChatHistoryQueryParams,
  ChatHistoryWithPlatform,
  PaginatedResult,
  SearchResult
} from '../types/database'

export interface UseChatHistoryState {
  chatHistory: ChatHistoryWithPlatform[]
  uniqueChats: ChatHistoryWithPlatform[]
  searchResults: ChatHistoryWithPlatform[]
  loading: boolean
  error: string | null
  hasMore: boolean
  total: number
  currentPage: number
}

export interface UseChatHistoryActions {
  createChat: (input: CreateChatHistoryInput) => Promise<ChatHistory | null>
  updateChat: (id: number, input: UpdateChatHistoryInput) => Promise<ChatHistory | null>
  deleteChat: (id: number) => Promise<boolean>
  loadChats: (params?: ChatHistoryQueryParams) => Promise<void>
  loadUniqueChats: (params?: ChatHistoryQueryParams) => Promise<void>
  searchChats: (query: string, limit?: number) => Promise<void>
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  clearError: () => void
  clearSearch: () => void
}

export interface UseChatHistoryReturn extends UseChatHistoryState, UseChatHistoryActions {}

/**
 * 聊天历史管理Hook
 */
export function useChatHistory(initialParams?: ChatHistoryQueryParams): UseChatHistoryReturn {
  const [state, setState] = useState<UseChatHistoryState>({
    chatHistory: [],
    uniqueChats: [],
    searchResults: [],
    loading: false,
    error: null,
    hasMore: false,
    total: 0,
    currentPage: 1
  })

  const [queryParams, setQueryParams] = useState<ChatHistoryQueryParams>(
    initialParams || { limit: 20, offset: 0 }
  )

  /**
   * 创建聊天记录
   */
  const createChat = useCallback(async (input: CreateChatHistoryInput): Promise<ChatHistory | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.create(input)
      
      if (result.success && result.data) {
        // 刷新数据
        await loadChats(queryParams)
        return result.data
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to create chat' 
        }))
        return null
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create chat'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [queryParams])

  /**
   * 更新聊天记录
   */
  const updateChat = useCallback(async (id: number, input: UpdateChatHistoryInput): Promise<ChatHistory | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.update(id, input)
      
      if (result.success && result.data) {
        // 更新本地状态
        setState(prev => ({
          ...prev,
          loading: false,
          chatHistory: prev.chatHistory.map(chat => 
            chat.id === id ? { ...chat, ...result.data } : chat
          )
        }))
        return result.data
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to update chat' 
        }))
        return null
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update chat'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [])

  /**
   * 删除聊天记录
   */
  const deleteChat = useCallback(async (id: number): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.delete(id)
      
      if (result.success) {
        // 从本地状态中移除
        setState(prev => ({
          ...prev,
          loading: false,
          chatHistory: prev.chatHistory.filter(chat => chat.id !== id),
          uniqueChats: prev.uniqueChats.filter(chat => chat.id !== id)
        }))
        return true
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to delete chat' 
        }))
        return false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete chat'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return false
    }
  }, [])

  /**
   * 加载聊天记录
   */
  const loadChats = useCallback(async (params?: ChatHistoryQueryParams): Promise<void> => {
    const finalParams = { ...queryParams, ...params }
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.getList(finalParams)
      
      if (result.success && result.data) {
        const { data, total, totalPages, page } = result.data
        const hasMore = page < totalPages

        setState(prev => ({
          ...prev,
          loading: false,
          chatHistory: finalParams.offset === 0 ? data : [...prev.chatHistory, ...data],
          total,
          hasMore,
          currentPage: page
        }))
        
        setQueryParams(finalParams)
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to load chats' 
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load chats'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [queryParams])

  /**
   * 加载去重的聊天记录
   */
  const loadUniqueChats = useCallback(async (params?: ChatHistoryQueryParams): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.getUniqueChats(params)
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          uniqueChats: result.data || []
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to load unique chats' 
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load unique chats'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [])

  /**
   * 搜索聊天记录
   */
  const searchChats = useCallback(async (query: string, limit: number = 20): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await chatHistoryDatabaseProxy.search(query, { limit })
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          searchResults: result.data || []
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to search chats' 
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search chats'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [])

  /**
   * 加载更多数据
   */
  const loadMore = useCallback(async (): Promise<void> => {
    if (!state.hasMore || state.loading) return

    const nextOffset = queryParams.offset! + (queryParams.limit || 20)
    await loadChats({ ...queryParams, offset: nextOffset })
  }, [state.hasMore, state.loading, queryParams, loadChats])

  /**
   * 刷新数据
   */
  const refresh = useCallback(async (): Promise<void> => {
    await loadChats({ ...queryParams, offset: 0 })
  }, [queryParams, loadChats])

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * 清除搜索结果
   */
  const clearSearch = useCallback(() => {
    setState(prev => ({ ...prev, searchResults: [] }))
  }, [])

  /**
   * 初始化加载数据
   */
  useEffect(() => {
    loadChats(queryParams)
  }, [])

  return {
    chatHistory: state.chatHistory,
    uniqueChats: state.uniqueChats,
    searchResults: state.searchResults,
    loading: state.loading,
    error: state.error,
    hasMore: state.hasMore,
    total: state.total,
    currentPage: state.currentPage,
    createChat,
    updateChat,
    deleteChat,
    loadChats,
    loadUniqueChats,
    searchChats,
    loadMore,
    refresh,
    clearError,
    clearSearch
  }
}

/**
 * 简化的聊天历史Hook，用于获取最近的去重聊天记录
 */
export function useRecentChats(limit: number = 10) {
  const [chats, setChats] = useState<ChatHistoryWithPlatform[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadRecentChats = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await chatHistoryDatabaseProxy.getUniqueChats({
        limit,
        order_direction: 'DESC'
      })
      
      if (result.success && result.data) {
        setChats(result.data)
      } else {
        setError(result.error || 'Failed to load recent chats')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load recent chats'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [limit])

  useEffect(() => {
    loadRecentChats()
  }, [loadRecentChats])

  return {
    chats,
    loading,
    error,
    refresh: loadRecentChats,
    clearError: () => setError(null)
  }
}
