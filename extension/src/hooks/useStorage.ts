import { useState, useEffect, useCallback } from 'react'
import { dexieDatabase } from '../lib/database/dexie'

export interface UseStorageState {
  isConnected: boolean
  isInitializing: boolean
  error: string | null
  lastError: string | null
}

export interface UseStorageActions {
  initialize: () => Promise<void>
  reconnect: () => Promise<void>
  close: () => Promise<void>
  clearError: () => void
}

export interface UseStorageReturn extends UseStorageState, UseStorageActions {}

/**
 * 数据库连接管理Hook
 */
export function useStorage(): UseStorageReturn {
  const [state, setState] = useState<UseStorageState>({
    isConnected: false,
    isInitializing: false,
    error: null,
    lastError: null
  })

  /**
   * 初始化数据库连接
   */
  const initialize = useCallback(async () => {
    setState(prev => ({
      ...prev,
      isInitializing: true,
      error: null
    }))

    try {
      await dexieDatabase.initialize()
      setState(prev => ({
        ...prev,
        isConnected: true,
        isInitializing: false,
        error: null
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database initialization failed'
      setState(prev => ({
        ...prev,
        isConnected: false,
        isInitializing: false,
        error: errorMessage,
        lastError: errorMessage
      }))
      throw error
    }
  }, [])

  /**
   * 重新连接数据库
   */
  const reconnect = useCallback(async () => {
    setState(prev => ({
      ...prev,
      isInitializing: true,
      error: null
    }))

    try {
      // 先关闭现有连接
      dexieDatabase.close()

      // 重新初始化
      await dexieDatabase.initialize()
      
      setState(prev => ({
        ...prev,
        isConnected: true,
        isInitializing: false,
        error: null
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database reconnection failed'
      setState(prev => ({
        ...prev,
        isConnected: false,
        isInitializing: false,
        error: errorMessage,
        lastError: errorMessage
      }))
      throw error
    }
  }, [])

  /**
   * 关闭数据库连接
   */
  const close = useCallback(async () => {
    try {
      dexieDatabase.close()
      setState(prev => ({
        ...prev,
        isConnected: false,
        error: null
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database close failed'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        lastError: errorMessage
      }))
      throw error
    }
  }, [])

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }))
  }, [])

  /**
   * 组件挂载时自动初始化
   */
  useEffect(() => {
    initialize().catch(error => {
      console.error('Auto-initialization failed:', error)
    })

    // 组件卸载时关闭连接
    return () => {
      close().catch(error => {
        console.error('Auto-close failed:', error)
      })
    }
  }, [])

  /**
   * 监听数据库连接状态
   */
  useEffect(() => {
    // Dexie 自动管理连接，不需要定期检查
    // 连接状态在初始化和错误时更新
  }, [])

  return {
    isConnected: state.isConnected,
    isInitializing: state.isInitializing,
    error: state.error,
    lastError: state.lastError,
    initialize,
    reconnect,
    close,
    clearError
  }
}

/**
 * 数据库事务Hook
 */
export function useTransaction() {
  const [isInTransaction, setIsInTransaction] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * 执行事务
   */
  const executeTransaction = useCallback(async <T>(
    callback: () => Promise<T>
  ): Promise<T> => {
    if (isInTransaction) {
      throw new Error('Already in transaction')
    }

    setIsInTransaction(true)
    setError(null)

    try {
      // Dexie 使用自己的事务管理
      const result = await dexieDatabase.transaction('rw', [dexieDatabase.chatHistory, dexieDatabase.platform], callback)
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Transaction failed'
      setError(errorMessage)
      throw error
    } finally {
      setIsInTransaction(false)
    }
  }, [isInTransaction])

  return {
    isInTransaction,
    error,
    executeTransaction,
    clearError: () => setError(null)
  }
}

/**
 * 数据库查询状态Hook
 */
export function useQuery<T>(
  queryFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await queryFn()
      setData(result)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Query failed'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, dependencies)

  useEffect(() => {
    execute()
  }, [execute])

  const refetch = useCallback(() => {
    execute()
  }, [execute])

  return {
    data,
    loading,
    error,
    refetch,
    clearError: () => setError(null)
  }
}
