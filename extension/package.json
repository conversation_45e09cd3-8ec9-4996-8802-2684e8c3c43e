{"name": "echosync-extension", "version": "1.0.0", "description": "EchoSync - AI提示词同步器", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-icons": "^1.3.0", "@radix-ui/themes": "^2.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dexie": "^4.0.11", "lucide-react": "^0.263.1", "nanoid": "^4.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.2", "@rollup/plugin-typescript": "^12.1.4", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/chrome": "^0.0.246", "@types/jest": "^29.5.5", "@types/node": "^20.5.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest": "^29.6.4", "jest-environment-jsdom": "^29.6.4", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "^5.0.0"}, "keywords": ["chrome-extension", "ai", "prompt", "sync", "react", "typescript"], "author": "EchoSync Team", "license": "MIT"}