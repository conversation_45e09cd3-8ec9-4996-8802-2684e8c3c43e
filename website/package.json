{"name": "echosync-website", "version": "1.0.0", "description": "EchoSync官方网站 - AI提示词同步器", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:generate": "supabase gen types typescript --project-id $SUPABASE_PROJECT_ID --schema public > types/supabase.ts", "db:reset": "supabase db reset", "db:seed": "tsx scripts/seed.ts"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "stripe": "^14.9.0", "@stripe/stripe-js": "^2.4.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "lucide-react": "^0.263.1", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "eslint": "^8.0.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "tsx": "^4.6.2", "supabase": "^1.123.4"}, "keywords": ["nextjs", "ai", "prompt", "sync", "saas", "supabase", "stripe"], "author": "EchoSync Team", "license": "MIT"}