# Vite 配置说明文档

本文档详细说明了 EchoAI Extension 项目中 `vite.config.ts` 文件的各项配置及其作用。

## 📋 配置概览

本项目使用 Vite 作为构建工具，配合 @crxjs/vite-plugin 插件来构建 Chrome 扩展。配置文件支持开发和生产两种模式，并针对 Chrome 扩展的特殊需求进行了优化。

## 🔧 核心配置项

### 1. 插件配置 (plugins)

#### 1.1 React 插件
```typescript
react({
  babel: {
    parserOpts: {
      plugins: ['decorators-legacy', 'classProperties']
    }
  }
})
```

**作用**: 启用 React 支持
- **decorators-legacy**: 支持装饰器语法（旧版本）
- **classProperties**: 支持类属性语法
- **适用场景**: React 组件开发，特别是使用装饰器模式的状态管理

#### 1.2 CRX 插件
```typescript
crx({
  manifest,
  contentScripts: {
    injectCss: true
  },
  browser: 'chrome'
})
```

**作用**: Chrome 扩展构建支持
- **manifest**: 引用 manifest.json 配置
- **injectCss**: 自动注入 CSS 到内容脚本
- **browser**: 指定目标浏览器为 Chrome
- **功能**: 处理扩展特有的构建需求，如背景脚本、内容脚本等

### 2. 编译配置 (esbuild)

```typescript
esbuild: {
  target: 'es2018'
}
```

**作用**: 设置 JavaScript 编译目标
- **target**: 编译到 ES2018 标准
- **兼容性**: 确保在较老版本的 Chrome 浏览器中运行
- **性能**: 平衡现代语法支持和兼容性

### 3. 路径解析 (resolve)

```typescript
resolve: {
  alias: {
    '@': path.resolve(__dirname, './src')
  }
}
```

**作用**: 配置模块路径别名
- **@**: 指向 `./src` 目录
- **优势**: 简化导入路径，避免相对路径嵌套
- **示例**: `import Component from '@/components/Component'`

### 4. 构建配置 (build)

```typescript
build: {
  outDir: 'dist',
  emptyOutDir: true,
  minify: mode === 'production',
  sourcemap: command === 'serve',
  rollupOptions: {
    output: {
      inlineDynamicImports: false,
      manualChunks: undefined
    }
  }
}
```

**详细说明**:
- **outDir**: 构建输出目录为 `dist`
- **emptyOutDir**: 构建前清空输出目录
- **minify**: 生产模式下启用代码压缩
- **sourcemap**: 开发模式下生成源码映射
- **inlineDynamicImports**: 禁用动态导入内联（适合扩展环境）
- **manualChunks**: 不手动分割代码块

### 5. 环境变量定义 (define)

```typescript
define: {
  'process.env.NODE_ENV': JSON.stringify(mode === 'production' ? 'production' : 'development')
}
```

**作用**: 定义全局常量
- **NODE_ENV**: 根据构建模式设置环境变量
- **用途**: 代码中可以通过 `process.env.NODE_ENV` 判断运行环境
- **优化**: 生产构建时会进行死代码消除

## 🌐 开发服务器配置 (server)

### 6.1 基础服务器配置

```typescript
server: {
  port: 5173,
  strictPort: true,
  hmr: {
    port: 5174
  }
}
```

**配置说明**:
- **port**: 开发服务器端口 5173
- **strictPort**: 端口被占用时不自动尝试其他端口
- **hmr.port**: 热更新服务端口 5174

### 6.2 CORS 配置

```typescript
cors: {
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'Cache-Control']
}
```

**作用**: 跨域资源共享配置
- **origin**: 允许所有来源
- **credentials**: 允许携带凭证
- **methods**: 允许的 HTTP 方法
- **allowedHeaders**: 允许的请求头
- **必要性**: Chrome 扩展需要与网页进行跨域通信

### 6.3 响应头配置

```typescript
headers: {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control'
}
```

**作用**: 设置默认响应头
- **安全性**: 允许跨域访问
- **兼容性**: 确保扩展与网页的通信正常

## 🔧 服务器中间件 (configureServer)

```typescript
configureServer(server) {
  server.middlewares.use((req, res, next) => {
    if (req.method === 'OPTIONS') {
      res.setHeader('Access-Control-Allow-Origin', '*')
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control')
      res.setHeader('Access-Control-Max-Age', '86400')
      res.statusCode = 200
      res.end()
      return
    }
    next()
  })
}
```

**功能**: 自定义服务器中间件
- **OPTIONS 处理**: 专门处理预检请求
- **CORS 头**: 设置跨域相关响应头
- **Max-Age**: 预检请求缓存时间 24 小时
- **目的**: 解决 Chrome 扩展开发中的跨域问题

## 📊 配置模式说明

### 开发模式 (command === 'serve')
- 启用源码映射 (sourcemap)
- 启用热更新 (HMR)
- 不压缩代码
- 详细的错误信息

### 生产模式 (mode === 'production')
- 启用代码压缩 (minify)
- 禁用源码映射
- 优化构建体积
- 设置生产环境变量

## 🎯 Chrome 扩展特殊配置

### 1. 内容安全策略 (CSP) 兼容
- 禁用内联动态导入
- 避免 eval() 相关代码
- 确保符合扩展安全要求

### 2. 模块加载优化
- 使用 ES2018 目标确保兼容性
- 合理的代码分割策略
- 优化扩展加载性能

### 3. 开发体验优化
- 完整的 CORS 支持
- 热更新功能
- 清晰的错误提示

## 🔍 常见问题与解决方案

### 1. 跨域问题
**问题**: 扩展与网页通信失败
**解决**: 已配置完整的 CORS 支持和预检请求处理

### 2. 热更新问题
**问题**: 代码修改后扩展不更新
**解决**: 使用独立的 HMR 端口 5174，避免冲突

### 3. 构建体积问题
**问题**: 扩展包过大
**解决**: 生产模式启用压缩，禁用不必要的功能

### 4. 兼容性问题
**问题**: 在旧版 Chrome 中无法运行
**解决**: 使用 ES2018 编译目标，确保广泛兼容

## 📝 维护建议

1. **定期更新**: 保持 Vite 和相关插件的最新版本
2. **性能监控**: 关注构建时间和包体积变化
3. **安全检查**: 定期审查 CORS 配置的安全性
4. **兼容性测试**: 在不同版本的 Chrome 中测试扩展功能
5. **配置优化**: 根据项目需求调整构建配置

## 🚀 扩展配置

如需添加新的配置项，建议：
1. 遵循 Vite 官方文档
2. 考虑 Chrome 扩展的特殊限制
3. 保持开发和生产环境的一致性
4. 添加相应的文档说明

---

**注意**: 修改配置后需要重启开发服务器才能生效。生产构建前请确保所有配置都经过充分测试。