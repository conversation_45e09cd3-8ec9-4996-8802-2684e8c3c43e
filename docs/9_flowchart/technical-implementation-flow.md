# EchoAI插件 - 技术实现详细流程

## 完整技术实现流程图

### 1. 系统初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Content as Content Script
    participant Adapter as AI Adapter
    participant Input as Input Manager
    participant Bubble as Floating Bubble
    participant History as History Manager
    participant Background as Background Script
    participant DB as IndexedDB

    User->>Content: 访问AI平台页面
    Content->>Content: detectPlatform()
    Content->>Adapter: 创建对应平台适配器
    Adapter->>Input: 初始化InputManager
    Adapter->>Bubble: 初始化FloatingBubble
    Adapter->>History: 初始化HistoryManager
    Content->>Content: setupEventListeners()
    Background->>DB: dexieDatabase.initialize()
    DB-->>Background: 数据库就绪
```

### 2. Prompt捕捉流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant DOM as DOM元素
    participant Input as InputManager
    participant Content as Content Script
    participant Messaging as MessagingService
    participant Background as Background Script
    participant Platform as PlatformService
    participant ChatHistory as ChatHistoryService
    participant DB as IndexedDB

    User->>DOM: 在输入框输入内容
    DOM->>Input: input事件触发
    Input->>Input: getCurrentInput()
    Input->>Content: 防抖处理(1000ms)
    Content->>Content: capturePrompt()
    
    alt 内容长度 > 10
        Content->>Messaging: sendToBackground(SYNC_PROMPT)
        Messaging->>Background: 消息传递
        Background->>Background: handleSyncPrompt()
        Background->>Platform: getByName(platformName)
        Platform-->>Background: 返回平台信息
        Background->>ChatHistory: create(promptData)
        ChatHistory->>DB: 存储到IndexedDB
        DB-->>ChatHistory: 存储成功
        ChatHistory-->>Background: 返回结果
        
        alt 实时同步开启
            Background->>Background: 获取所有支持的标签页
            Background->>Messaging: sendToAllTabs(INJECT_PROMPT)
        end
    end
```

### 3. 历史提示词获取流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Bubble as FloatingBubble
    participant History as HistoryManager
    participant HistoryBubble as HistoryBubble
    participant ChatHistory as ChatHistoryService
    participant DB as IndexedDB

    User->>Bubble: 鼠标悬停在浮动气泡
    Bubble->>Bubble: mouseenter事件
    Bubble->>Bubble: 延迟500ms
    Bubble->>History: showHistoryBubble()
    History->>ChatHistory: getUniqueChats({limit: 10})
    ChatHistory->>DB: 查询IndexedDB
    DB-->>ChatHistory: 返回去重历史记录
    ChatHistory-->>History: 返回数据
    History->>HistoryBubble: updateHistory(data)
    HistoryBubble->>HistoryBubble: renderContent()
    HistoryBubble->>HistoryBubble: show(anchorElement)
    HistoryBubble-->>User: 显示历史提示词列表
```

### 4. 提示词注入流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant HistoryBubble as HistoryBubble
    participant History as HistoryManager
    participant Adapter as AIAdapter
    participant Input as InputManager
    participant DOM as DOM元素

    User->>HistoryBubble: 点击历史提示词
    HistoryBubble->>HistoryBubble: handleItemClick()
    HistoryBubble->>HistoryBubble: 触发echosync:history-item-click事件
    HistoryBubble->>History: 事件监听器接收
    History->>History: handleHistoryItemClick()
    History->>History: 触发echosync:inject-prompt事件
    History->>Adapter: 事件监听器接收
    Adapter->>Input: injectPrompt(prompt)
    
    alt 输入框类型判断
        Input->>DOM: TEXTAREA/INPUT设置value
        Input->>DOM: contentEditable设置textContent
    end
    
    Input->>DOM: 触发input事件
    Input->>DOM: 设置焦点
    HistoryBubble->>HistoryBubble: hide()
    HistoryBubble-->>User: 显示成功提示
```

## 关键技术实现细节

### 1. 平台检测机制

```typescript
// content/index.ts
detectPlatform(): AIAdapter | null {
  const hostname = window.location.hostname
  
  switch (hostname) {
    case 'chat.openai.com':
      return new ChatGPTAdapter()
    case 'chat.deepseek.com':
      return new DeepSeekAdapter()
    case 'claude.ai':
      return new ClaudeAdapter()
    // ... 其他平台
    default:
      return null
  }
}
```

### 2. 消息传递机制

```typescript
// lib/messaging.ts
export class MessagingService {
  static async sendToBackground<T = any>(
    type: MessageType,
    payload?: any
  ): Promise<T> {
    const message: ChromeMessage = {
      type,
      payload,
      timestamp: Date.now()
    }
    
    return await chrome.runtime.sendMessage(message)
  }
  
  static onMessage<T = any>(callback: Function): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      const result = callback(message, sender, sendResponse)
      if (result instanceof Promise) {
        result.then(sendResponse).catch(error => sendResponse({ error }))
        return true // 保持消息通道开放
      }
    })
  }
}
```

### 3. IndexedDB存储机制

```typescript
// lib/storage/chatHistoryDexie.ts
export class ChatHistoryService {
  async create(data: Partial<ChatHistory>): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      
      const chatHistory: ChatHistory = {
        id: Date.now(),
        chat_prompt: data.chat_prompt!,
        platform_id: data.platform_id!,
        chat_uid: data.chat_uid || Date.now().toString(),
        create_time: data.create_time || Date.now(),
        is_delete: 0
      }
      
      const id = await dexieDatabase.chatHistory.add(chatHistory)
      return { success: true, data: { ...chatHistory, id } }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
}
```

### 4. 事件系统机制

```typescript
// content/base/AIAdapter.ts
setupEventListeners(): void {
  // 自定义事件监听
  document.addEventListener('echosync:inject-prompt', (event: any) => {
    const { prompt } = event.detail
    this.inputManager.injectPrompt(prompt)
  })
  
  document.addEventListener('echosync:history-item-click', (event: any) => {
    const { chat } = event.detail
    this.handleHistoryItemClick(chat)
  })
}

// 事件触发
document.dispatchEvent(new CustomEvent('echosync:inject-prompt', {
  detail: { prompt: selectedPrompt }
}))
```

### 5. UI组件渲染机制

```typescript
// components/HistoryBubble.ts
export class HistoryBubble {
  private renderContent(): void {
    if (!this.container) return
    
    this.container.innerHTML = ''
    
    // 渲染标题
    const header = document.createElement('div')
    header.className = 'echosync-history-header'
    header.innerHTML = '<h3 class="echosync-history-title">最近提示词</h3>'
    this.container.appendChild(header)
    
    // 渲染历史记录
    this.chatHistory.forEach((chat, index) => {
      const item = this.createHistoryItem(chat, index)
      this.container.appendChild(item)
    })
  }
  
  public show(anchorElement: HTMLElement): void {
    // 计算位置
    const rect = anchorElement.getBoundingClientRect()
    const bubbleHeight = Math.min(400, this.chatHistory.length * 60 + 60)
    
    // 设置位置和显示
    this.container.style.top = `${rect.top - bubbleHeight - 10}px`
    this.container.style.left = `${rect.left}px`
    this.container.style.display = 'block'
    
    // 动画效果
    requestAnimationFrame(() => {
      this.container.style.opacity = '1'
      this.container.style.transform = 'scale(1) translateY(0)'
    })
  }
}
```

## 性能优化策略

### 1. 防抖处理
```typescript
// 输入事件防抖
let captureTimeout: number | null = null
document.addEventListener('input', (event) => {
  if (captureTimeout) clearTimeout(captureTimeout)
  captureTimeout = setTimeout(capturePrompt, 1000)
})
```

### 2. 内存管理
```typescript
// 组件销毁时清理资源
destroy(): void {
  if (this.bubble) {
    this.bubble.remove()
    this.bubble = null
  }
  if (this.historyBubble) {
    this.historyBubble.destroy()
    this.historyBubble = null
  }
}
```

### 3. 查询优化
```typescript
// 使用索引和限制查询
async getUniqueChats(params: { limit?: number }): Promise<DatabaseResult<ChatHistoryWithPlatform[]>> {
  const result = await dexieDatabase.getChatHistoryWithPlatform({
    limit: params.limit || 10,
    order_by: 'create_time',
    order_direction: 'DESC'
  })
  return { success: true, data: result }
}
```

## 错误处理机制

### 1. 异步错误处理
```typescript
try {
  const result = await chatHistoryService.create(promptData)
  if (result.success) {
    console.log('Prompt saved:', result.data)
  } else {
    console.error('Save failed:', result.error)
  }
} catch (error) {
  console.error('Unexpected error:', error)
}
```

### 2. 消息传递错误处理
```typescript
MessagingService.onMessage(async (message, sender, sendResponse) => {
  try {
    switch (message.type) {
      case MessageType.SYNC_PROMPT:
        await handleSyncPrompt(message.payload, sender)
        sendResponse({ success: true })
        break
      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  } catch (error) {
    sendResponse({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
  }
})
```
