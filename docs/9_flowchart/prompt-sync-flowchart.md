# EchoAI插件 - Prompt捕捉与同步流程图

## 完整流程概述

本文档详细描述了EchoAI插件中从A页面捕捉prompt，通过message存入IndexedDB，然后在B页面获取历史提示词并输入到当前页面输入框的完整流程。

## 主要组件和文件

### 核心文件结构
```
extension/src/
├── content/
│   ├── index.ts                    # Content Script主入口
│   ├── base/
│   │   ├── AIAdapter.ts            # AI平台适配器基类
│   │   ├── InputManager.ts         # 输入管理器
│   │   ├── ArchiveButton.ts        # 存档按钮管理
│   │   ├── FloatingBubble.ts       # 浮动气泡管理
│   │   └── HistoryManager.ts       # 历史记录管理
│   └── adapters/
│       ├── chatgpt.ts              # ChatGPT适配器
│       ├── deepseek.ts             # DeepSeek适配器
│       ├── claude.ts               # Claude适配器
│       └── ...                     # 其他平台适配器
├── background/
│   └── index.ts                    # Background Service Worker
├── lib/
│   ├── messaging.ts                # 消息传递服务
│   └── storage/
│       └── chatHistoryDexie.ts     # IndexedDB存储服务
├── components/
│   └── HistoryBubble.ts            # 历史气泡UI组件
└── types/
    ├── index.ts                    # 基础类型定义
    └── database.ts                 # 数据库类型定义
```

## 详细流程图

### 阶段1: A页面Prompt捕捉

```mermaid
graph TD
    A["用户在A页面输入Prompt"] --> B{"检测输入事件"}
    B --> C["InputManager.getCurrentInput()"]
    C --> D["AIAdapter.setupEventListeners()"]
    D --> E{"输入内容长度 > 10?"}
    E -->|是| F["触发capturePrompt函数"]
    E -->|否| G["跳过捕捉"]

    F --> H["MessagingService.sendToBackground()"]
    H --> I["MessageType.SYNC_PROMPT"]
    I --> J["Background Script接收消息"]

    J --> K["handleSyncPrompt函数"]
    K --> L["platformService.getByName()"]
    L --> M["获取平台信息"]
    M --> N["chatHistoryService.create()"]
    N --> O["存储到IndexedDB"]

    O --> P{"实时同步开启?"}
    P -->|是| Q["获取所有支持的标签页"]
    P -->|否| R["流程结束"]
    Q --> S["向其他页面发送INJECT_PROMPT消息"]
    S --> R
```

### 阶段2: IndexedDB存储详情

```mermaid
graph TD
    A["chatHistoryService.create()"] --> B["dexieDatabase.initialize()"]
    B --> C["构造ChatHistory对象"]
    C --> D{"检查是否已存在相同prompt?"}
    D -->|是| E["更新现有记录"]
    D -->|否| F["创建新记录"]

    E --> G["chatHistory.put()"]
    F --> G
    G --> H["IndexedDB存储完成"]
    H --> I["返回DatabaseResult"]

    C --> J["数据结构"]
    J --> K["chat_prompt: string"]
    J --> L["platform_id: number"]
    J --> M["chat_uid: string"]
    J --> N["create_time: number"]
    J --> O["is_delete: 0"]
```

### 阶段3: B页面历史提示词获取

```mermaid
graph TD
    A["用户在B页面悬停浮动气泡"] --> B["FloatingBubble.mouseenter事件"]
    B --> C["延迟500ms后触发"]
    C --> D["showHistoryBubble()"]
    D --> E["HistoryManager.showHistoryBubble()"]

    E --> F["chatHistoryService.getUniqueChats()"]
    F --> G["dexieDatabase.getChatHistoryWithPlatform()"]
    G --> H["查询IndexedDB"]
    H --> I["获取去重的历史记录"]

    I --> J["HistoryBubble.updateHistory()"]
    J --> K["渲染历史气泡UI"]
    K --> L["显示最近10条提示词"]
    L --> M["用户可点击选择"]
```

### 阶段4: 提示词注入到输入框

```mermaid
graph TD
    A["用户点击历史提示词"] --> B["HistoryBubble.handleItemClick()"]
    B --> C["触发echosync:history-item-click事件"]
    C --> D["HistoryManager.handleHistoryItemClick()"]
    D --> E["触发echosync:inject-prompt事件"]

    E --> F["AIAdapter监听事件"]
    F --> G["InputManager.injectPrompt()"]
    G --> H{"判断输入框类型"}
    H -->|"TEXTAREA/INPUT"| I["设置element.value"]
    H -->|"contentEditable"| J["设置element.textContent"]

    I --> K["触发input事件"]
    J --> K
    K --> L["更新输入框内容"]
    L --> M["HistoryBubble.hide()"]
    M --> N["显示成功提示"]
```

## 关键方法和事件

### Content Script (content/index.ts)
- `setupEventListeners()`: 设置消息监听器
- `captureCurrentPrompt()`: 捕捉当前输入内容
- `injectPrompt(prompt)`: 注入提示词到输入框

### Background Script (background/index.ts)
- `handleSyncPrompt(promptData, sender)`: 处理提示词同步
- `handleCapturePrompt(data, sender)`: 处理提示词捕获

### InputManager (content/base/InputManager.ts)
- `getCurrentInput()`: 获取当前输入内容
- `injectPrompt(prompt)`: 注入提示词
- `handleSendEvent()`: 处理发送事件

### ArchiveButton (content/base/ArchiveButton.ts)
- `autoArchivePrompt(promptContent, platform)`: 自动存档提示词

### FloatingBubble (content/base/FloatingBubble.ts)
- `createFloatingBubble()`: 创建浮动气泡
- `showHistoryBubble()`: 显示历史气泡

### HistoryManager (content/base/HistoryManager.ts)
- `showHistoryBubble()`: 显示历史气泡
- `handleHistoryItemClick(chat)`: 处理历史项点击

### HistoryBubble (components/HistoryBubble.ts)
- `updateHistory(history)`: 更新历史数据
- `show(anchorElement)`: 显示气泡
- `handleItemClick(chat, element)`: 处理项目点击

### MessagingService (lib/messaging.ts)
- `sendToBackground(type, payload)`: 发送消息到后台
- `sendToContentScript(tabId, type, payload)`: 发送消息到内容脚本
- `onMessage(callback)`: 监听消息

### ChatHistoryService (lib/storage/chatHistoryDexie.ts)
- `create(data)`: 创建聊天历史记录
- `getUniqueChats(params)`: 获取去重的聊天历史
- `getList(params)`: 获取聊天历史列表

## 消息类型 (MessageType)
- `SYNC_PROMPT`: 同步提示词
- `CAPTURE_PROMPT`: 捕获提示词
- `INJECT_PROMPT`: 注入提示词
- `GET_HISTORY`: 获取历史记录

## 自定义事件
- `echosync:auto-archive`: 自动存档事件
- `echosync:inject-prompt`: 注入提示词事件
- `echosync:history-item-click`: 历史项点击事件
- `echosync:handle-history-click`: 处理历史点击事件
- `echosync:show-stored-prompts`: 显示存储的提示词事件
