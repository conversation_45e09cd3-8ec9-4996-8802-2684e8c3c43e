# 移除自动捕捉提示词功能 - 任务规划

## 需求分析
移除页面自动捕捉提示词的功能，只保留两个特定场景的捕捉：
1. 主动点击注入的"存档"按钮时捕捉
2. 点击发送按钮向后端发送问题时捕捉

## 当前自动捕捉逻辑分析
根据重构后的代码结构，自动捕捉逻辑主要在以下位置：
- `AIAdapter.ts` 中的 `setupPromptCapture()` 方法
- `content/index.ts` 中的消息处理（CAPTURE_PROMPT类型）
- 输入事件监听和防抖处理

## 任务分解

### 1. 移除AIAdapter中的自动捕捉逻辑 ✅
- [x] 移除 `setupPromptCapture()` 方法中的输入事件监听
- [x] 移除防抖处理逻辑
- [x] 保留发送按钮点击时的捕捉逻辑（场景2）
- [x] 确保存档按钮的捕捉逻辑正常工作（场景1）

### 2. 更新content/index.ts的消息处理 ✅
- [x] 保留 CAPTURE_PROMPT 消息类型处理（用于主动捕捉）
- [x] 移除 SYNC_PROMPT 相关的自动同步逻辑
- [x] 确保手动捕捉功能正常

### 3. 检查存档按钮功能 ✅
- [x] 确认 ArchiveButton 的点击捕捉逻辑完整
- [x] 验证存档按钮能正确触发提示词捕捉
- [x] 测试存档功能的完整流程

### 4. 检查发送按钮捕捉逻辑 ✅
- [x] 确认发送按钮点击时的捕捉逻辑
- [x] 验证发送事件能正确触发提示词捕捉
- [x] 确保捕捉时机正确（发送前捕捉内容）

### 5. 清理相关代码 ✅
- [x] 移除不再使用的输入监听事件
- [x] 清理防抖相关的变量和逻辑
- [x] 重构发送按钮捕捉逻辑，避免重复监听

### 6. 测试验证 ✅
- [x] 构建测试通过
- [x] 编译无错误
- [x] 代码逻辑重构完成

## 实施顺序
1. 首先移除AIAdapter中的自动捕捉逻辑
2. 更新content/index.ts的消息处理
3. 验证和完善存档按钮功能
4. 验证和完善发送按钮捕捉功能
5. 清理冗余代码
6. 全面测试功能

## 注意事项
- 保持现有的手动捕捉API接口不变
- 确保存档和发送两个场景的捕捉逻辑完整
- 移除自动捕捉时要小心不影响其他功能
- 测试时要验证各个平台的兼容性
