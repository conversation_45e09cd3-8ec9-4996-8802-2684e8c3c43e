# Content输入捕捉业务流程重构总结

## 重构目标
重新梳理content/index.ts与adapter与base中InputManager.ts的捕捉输入的业务流程，解决结构混乱、职责不清、代码重复的问题。

## 重构原则
1. **单一职责原则**：每个类只负责一个明确的功能
2. **继承层次清晰**：最多2级继承，组合优于继承
3. **配置化扩展**：优先使用配置和正则匹配，而非完全重写
4. **钩子机制**：提供扩展点供子类处理特殊情况

## 重构前的问题

### 1. 职责混乱
- `content/index.ts` 既负责生命周期管理，又直接处理输入捕捉业务
- `AIAdapter.ts` 中有 `getCurrentInput()` 方法，但子类又重写了这个方法
- `InputManager.ts` 有完整的输入管理功能，但各个adapter又各自实现输入相关逻辑

### 2. 重复代码
- 所有adapter子类都有相同的 `injectPrompt` 实现
- 输入捕捉逻辑在多个地方重复

### 3. 继承层次不清晰
- 子类直接重写父类方法而不是扩展
- 缺乏统一的扩展机制

## 重构后的架构

### 1. content/index.ts - 纯生命周期管理
**职责**：
- 适配器检测和初始化
- 全局事件监听设置
- 消息路由处理

**移除的业务逻辑**：
- `setupPromptCapture()` - 移至AIAdapter
- `captureCurrentPrompt()` - 委托给adapter
- `injectPrompt()` - 委托给adapter
- `showNotification()` - 移除重复实现

### 2. AIAdapter.ts - 通用业务逻辑中心
**新增功能**：
- `setupBusinessLogic()` - 统一的业务逻辑设置入口
- `setupPromptCapture()` - 通用的提示词捕捉逻辑
- `getCurrentInput()` - 公开方法，支持子类扩展
- `customGetCurrentInput()` - 扩展钩子方法
- `injectPrompt()` - 默认实现，移除子类重复代码

**扩展机制**：
```typescript
// 子类可重写的钩子方法
protected customGetCurrentInput(): string | null {
  // 返回null表示使用默认实现
  // 返回字符串表示使用自定义实现
}

protected async setupCustomBusinessLogic(): Promise<void> {
  // 子类可添加额外的业务逻辑
}
```

### 3. InputManager.ts - 核心实现类
**新增接口**：
```typescript
interface InputDetectionConfig {
  selectors: string[]
  patterns?: RegExp[]
  customValidator?: (element: HTMLElement) => boolean
  contentExtractor?: (element: HTMLElement) => string
}

interface InputManagerConfig {
  inputField: string
  sendButton: string
  messageContainer?: string
  detection?: InputDetectionConfig
}
```

**新增方法**：
- `detectInputElement()` - 通用输入元素检测
- `extractContent()` - 配置化内容提取
- 支持正则匹配和自定义验证器

### 4. Adapter子类 - 配置化扩展
**KimiAdapter示例**：
```typescript
// 重构前：完全重写getCurrentInput方法
getCurrentInput(): string { /* 完整实现 */ }

// 重构后：只扩展特殊情况
protected customGetCurrentInput(): string | null {
  const inputElement = this.inputManager.getInputElement()
  if (inputElement?.hasAttribute('data-lexical-editor')) {
    return inputElement.textContent?.trim() || ''
  }
  return null // 使用默认实现
}
```

**移除的重复代码**：
- 所有子类的 `injectPrompt` 方法（统一使用基类实现）
- 重复的输入检测逻辑

## 重构成果

### 1. 代码结构清晰
- **index.ts**: 58行（重构前282行）- 纯生命周期管理
- **AIAdapter.ts**: 增加了通用业务逻辑和扩展机制
- **InputManager.ts**: 增加了配置化检测和扩展接口
- **子类**: 大幅简化，只保留平台特定的配置和扩展

### 2. 职责分工明确
```
index.ts (生命周期)
    ↓
AIAdapter.ts (通用业务逻辑)
    ↓
InputManager.ts (核心实现)
    ↓
子类 (平台特定扩展)
```

### 3. 扩展机制完善
- 支持配置化的输入检测
- 提供钩子方法供子类扩展
- 优先使用正则匹配而非完全重写

### 4. 代码复用性提高
- 移除了所有重复的 `injectPrompt` 实现
- 统一了输入捕捉逻辑
- 提供了可复用的检测和提取方法

## 测试验证
- ✅ 编译无错误
- ✅ 构建成功
- ✅ 保持了原有功能的完整性
- ✅ 特殊平台（如Kimi的Lexical编辑器）的兼容性

## 后续建议
1. 在实际使用中测试各平台的功能完整性
2. 根据需要进一步优化配置化检测规则
3. 考虑添加更多的扩展钩子以支持未来的平台特性
