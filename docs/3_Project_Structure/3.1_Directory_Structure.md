# 3.1 项目目录结构

项目采用 Monorepo（单一代码库）结构，将 Chrome 插件和官方网站放在同一个仓库中进行管理，便于代码复用和统一维护。

## 完整目录结构

```
EchoAIExtention/
├── .github/                    # GitHub Actions (CI/CD) 配置
│   └── workflows/
│       └── ci.yml              # 自动化测试和构建流程
├── .augment/                   # Augment AI 配置
│   └── rules/
│       └── augment-guideline.md # 开发规范和指导
├── docs/                       # 项目文档 (重构后)
│   ├── 1_Project_Research_and_Requirements/
│   ├── 2_Technology_Stack/
│   ├── 3_Project_Structure/
│   ├── 4_Development_Guide/
│   ├── 5_Project_Management/
│   ├── 6_User_Manual/
│   ├── 7_todo/                 # 开发任务和技术文档
│   ├── 8_storage/              # 存储相关文档
│   └── web/                    # 网站相关文档
├── extension/                  # 🔌 Chrome插件源码
│   ├── public/                 # 静态资源
│   │   ├── icons/              # 插件图标 (16, 32, 48, 128)
│   │   └── manifest.json       # 插件清单文件 (Manifest V3)
│   ├── src/                    # 源代码
│   │   ├── background/         # 后台脚本 (Service Worker)
│   │   │   └── index.ts        # 主要的后台脚本
│   │   ├── content/            # 内容脚本 (注入到AI网页)
│   │   │   ├── adapters/       # AI平台适配器
│   │   │   │   ├── base.ts     # 适配器基类
│   │   │   │   ├── chatgpt.ts  # ChatGPT适配器
│   │   │   │   ├── claude.ts   # Claude适配器
│   │   │   │   ├── deepseek.ts # DeepSeek适配器
│   │   │   │   ├── gemini.ts   # Gemini适配器
│   │   │   │   └── kimi.ts     # Kimi适配器
│   │   │   └── index.ts        # 内容脚本入口
│   │   ├── popup/              # 弹窗页面 (React应用)
│   │   │   ├── components/     # 弹窗专用组件
│   │   │   ├── pages/          # 弹窗页面
│   │   │   ├── App.tsx         # 弹窗主应用
│   │   │   ├── index.html      # 弹窗HTML模板
│   │   │   └── main.tsx        # 弹窗入口文件
│   │   ├── options/            # 选项页面 (React应用)
│   │   │   ├── OptionsApp.tsx  # 选项页面主应用
│   │   │   ├── index.html      # 选项页面HTML模板
│   │   │   └── main.tsx        # 选项页面入口
│   │   ├── components/         # 共享React组件
│   │   │   ├── ui/             # shadcn/ui 组件
│   │   │   │   ├── button.tsx  # 按钮组件
│   │   │   │   └── card.tsx    # 卡片组件
│   │   │   ├── HistoryBubble.ts # 历史记录气泡
│   │   │   └── PlatformIcon.ts # 平台图标组件
│   │   ├── lib/                # 公共库
│   │   │   ├── database/       # 数据库相关
│   │   │   │   └── dexie.ts    # Dexie数据库配置
│   │   │   ├── storage/        # 存储相关
│   │   │   │   └── chatHistoryDexie.ts # 聊天历史存储
│   │   │   ├── messaging.ts    # 消息通信
│   │   │   ├── storage.ts      # 存储工具
│   │   │   └── utils.ts        # 通用工具函数
│   │   ├── hooks/              # React Hooks
│   │   │   ├── useChatHistory.ts # 聊天历史Hook
│   │   │   ├── usePlatform.ts  # 平台相关Hook
│   │   │   └── useStorage.ts   # 存储Hook
│   │   ├── stores/             # 状态管理 (Zustand)
│   │   │   └── app-store.ts    # 应用状态管理
│   │   ├── types/              # TypeScript类型定义
│   │   │   ├── database.ts     # 数据库类型
│   │   │   └── index.ts        # 通用类型
│   │   ├── styles/             # 全局样式
│   │   │   └── globals.css     # 全局CSS样式
│   │   └── setupTests.ts       # 测试环境配置
│   ├── dist/                   # Vite构建输出目录
│   ├── node_modules/           # 插件依赖包
│   ├── package.json            # 插件的依赖配置
│   ├── vite.config.ts          # Vite 构建配置
│   ├── tsconfig.json           # TypeScript 配置
│   ├── tsconfig.node.json      # Node.js TypeScript配置
│   ├── tailwind.config.js      # Tailwind CSS 配置
│   ├── postcss.config.js       # PostCSS 配置
│   └── jest.config.js          # Jest 测试配置
├── website/                    # 🌐 官方网站源码 (Next.js)
│   ├── src/
│   │   ├── app/                # Next.js App Router 核心目录
│   │   ├── components/         # 网站共享的React组件
│   │   ├── lib/                # Supabase, Stripe等客户端
│   │   └── types/              # 网站类型定义
│   ├── public/                 # 网站的静态资源
│   ├── node_modules/           # 网站依赖包
│   ├── package.json            # 网站的依赖配置
│   ├── next.config.js          # Next.js 配置
│   ├── tailwind.config.js      # Tailwind CSS 配置
│   ├── tsconfig.json           # TypeScript 配置
│   └── .env.example            # 环境变量模板
├── node_modules/               # Monorepo 共享依赖
├── .gitignore                  # Git忽略文件配置
├── package.json                # Monorepo 根依赖和工作区脚本
├── package-lock.json           # 依赖锁定文件
└── README.md                   # 项目总述
```

## 核心目录职责

### 🔌 Chrome插件 (`extension/`)

独立完整的Chrome插件项目，采用现代化的前端技术栈。

#### 核心模块
-   **`src/background/`**: Service Worker后台脚本
    -   负责处理长期运行的后台任务和消息中转
    -   管理插件的生命周期和全局状态
    -   处理跨页面的数据同步和通信

-   **`src/content/`**: 内容脚本和平台适配器
    -   负责与AI聊天网站的DOM进行交互
    -   实现提示词捕获和注入的核心功能
    -   包含多个AI平台的适配器（ChatGPT、Claude、Gemini等）

-   **`src/popup/`**: 弹窗界面
    -   用户点击插件图标时出现的弹窗界面
    -   提供快速访问和操作功能
    -   基于React构建的单页应用

-   **`src/options/`**: 选项设置页面
    -   插件的详细设置和配置页面
    -   用户偏好设置和高级功能配置
    -   独立的React应用

#### 支持模块
-   **`src/components/`**: 共享React组件
    -   包含shadcn/ui组件库的定制组件
    -   平台图标、历史记录气泡等专用组件

-   **`src/lib/`**: 核心工具库
    -   数据库操作（Dexie + IndexedDB）
    -   存储管理（Chrome Storage API）
    -   消息通信和工具函数

-   **`src/hooks/`**: React Hooks
    -   封装常用的状态逻辑和副作用
    -   提供类型安全的数据访问接口

-   **`src/stores/`**: 状态管理
    -   基于Zustand的全局状态管理
    -   跨组件的状态共享和同步

-   **`src/types/`**: TypeScript类型定义
    -   数据库模型和接口定义
    -   确保类型安全和代码提示

### 🌐 官方网站 (`website/`)

独立完整的Next.js全栈网站项目，提供用户注册、订阅和云端同步功能。

-   **`src/app/`**: Next.js App Router核心
    -   包含所有页面路由、API路由和布局
    -   支持SSR/SSG的现代化路由系统

-   **`src/components/`**: 网站组件
    -   与插件共享设计系统的React组件
    -   营销页面、用户界面等专用组件

-   **`src/lib/`**: 第三方服务集成
    -   封装与Supabase、Stripe等服务的交互逻辑
    -   提供类型安全的API客户端

### 📚 项目文档 (`docs/`)

完整的项目文档体系，涵盖需求、设计、开发和用户指南。

-   **结构化文档**: 按功能模块组织的文档结构
-   **开发指南**: 详细的开发流程和技术规范
-   **API文档**: 接口定义和使用说明
-   **用户手册**: 面向最终用户的使用指南

### 🔧 配置和工具

-   **`.github/`**: GitHub Actions CI/CD配置
    -   自动化测试、构建和部署流程
    -   代码质量检查和安全扫描

-   **`.augment/`**: Augment AI开发配置
    -   AI辅助开发的规范和指导
    -   项目特定的开发约定

-   **根 `package.json`**: Monorepo工作区配置
    -   定义项目的工作区（workspaces）
    -   提供顶层命令统一管理插件和网站
