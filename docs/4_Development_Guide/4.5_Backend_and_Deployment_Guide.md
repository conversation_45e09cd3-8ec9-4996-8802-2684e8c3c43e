# 4.5 后端与部署实战指南

本指南提供了在 EchoSync 官网项目中设置和部署后端服务（数据库、认证、支付）的详细步骤。

---

## 1. 使用 Vercel 部署

Vercel 是 Next.js 的首选部署平台，提供无缝的开发和部署体验。

### 1.1 关联项目

1.  使用你的 GitHub 账户登录 [Vercel](https://vercel.com/)。
2.  在仪表盘中，点击 "Add New..." -> "Project"。
3.  选择你的 EchoSync GitHub 仓库并点击 "Import"。
4.  Vercel 会自动检测到这是一个 Next.js 项目。在 "Configure Project" 页面：
    -   **Framework Preset**: 确认为 `Next.js`。
    -   **Root Directory**: 确保设置为 `website`，因为我们的项目在 Monorepo 的子目录中。
    -   点击 **Deploy**。

### 1.2 配置环境变量

你的网站需要 API 密钥才能连接到 Supabase 和 Stripe。这些密钥绝不能硬编码在代码中。

1.  在 Vercel 的项目仪表盘中，进入 "Settings" -> "Environment Variables"。
2.  将你在 `website/.env.local` 文件中使用的所有密钥（如 `NEXT_PUBLIC_SUPABASE_URL`, `STRIPE_SECRET_KEY` 等）逐一添加进去。
3.  确保为 **Production, Preview, 和 Development** 环境都配置了这些变量。

### 1.3 生产与预览部署

-   **生产部署**: 每当代码合并到 `main` 分支，Vercel 会自动构建并部署到你的生产域名。
-   **预览部署**: 每个提交到 Pull Request 的 commit 都会生成一个唯一的预览 URL。这使得团队可以在合并前在真实环境中审查变更。

---

## 2. Supabase: 数据库与认证集成

Supabase 为我们提供了一个完整的后端，包括 PostgreSQL 数据库、用户认证等。

### 2.1 创建 Supabase 项目

1.  登录 [Supabase](https://supabase.com/) 并创建一个新项目。
2.  在项目仪表盘的 "Settings" -> "API" 中，找到你的 **Project URL** 和 **`anon` public key**。将这些值填入 Vercel 的环境变量中。

### 2.2 数据库管理

#### a. 设计表结构

在 Supabase 仪表盘的 "SQL Editor" 中，运行 SQL 命令来创建你的数据表。

```sql
-- 示例：创建 prompts 表
CREATE TABLE prompts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  platform TEXT NOT NULL,
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 为表开启 Row Level Security (RLS)
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;

-- 创建策略：只允许用户访问自己的数据
CREATE POLICY "User can view their own prompts" ON prompts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "User can insert their own prompts" ON prompts
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```
**注意**: 启用 RLS 并创建安全策略是保护用户数据的关键步骤。

#### b. 生成数据库类型

为了实现端到端的类型安全，我们可以使用 Supabase CLI 生成类型定义。

```bash
# 1. 登录 Supabase CLI
npx supabase login

# 2. 链接到你的项目
npx supabase link --project-ref <your-project-id>

# 3. 生成类型并保存到文件中
npx supabase gen types typescript --linked > website/src/types/database.ts
```
现在，当你在代码中使用 Supabase 客户端时，就会有完整的 TypeScript 类型提示和检查。

### 2.3 认证集成

Supabase Auth 提供了多种登录方式。

1.  **配置提供商**: 在 Supabase 仪表盘的 "Authentication" -> "Providers" 中，启用你希望支持的登录方式（如 Email, Google, GitHub）。
2.  **集成 Auth Helpers**: 使用 `@supabase/auth-helpers-nextjs` 简化在 Next.js (App Router) 中的认证流程。
3.  **创建受保护的路由**:
    ```tsx
    // website/src/components/ProtectedRoute.tsx
    import { useAuth } from '@/hooks/useAuth'; // 一个包含Supabase逻辑的自定义Hook
    import { redirect } from 'next/navigation';

    export function ProtectedRoute({ children }: { children: React.ReactNode }) {
      const { user, loading } = useAuth();

      if (loading) {
        return <div>Loading...</div>; // 或者一个骨架屏
      }

      if (!user) {
        redirect('/login');
      }

      return <>{children}</>;
    }
    ```
    然后你可以在 `dashboard/layout.tsx` 中使用这个组件来保护整个仪表板页面。

---

## 3. Stripe: 支付系统集成

Stripe 用于处理订阅付费。

### 3.1 创建产品和价格

1.  登录 [Stripe Dashboard](https://dashboard.stripe.com/)。
2.  进入 "Products" 目录，点击 "Add product" 创建你的订阅产品，例如 "专业版"。
3.  为产品添加定价方案（Pricing model），例如 "$4.99/month"。记下生成的价格ID (`price_...`)。

### 3.2 实现支付流程

支付流程分为两步：创建支付会话，和处理支付结果。

#### a. API: 创建 Checkout Session

创建一个API路由来处理支付请求。

```typescript
// website/src/app/api/create-checkout-session/route.ts
import { stripe } from '@/lib/stripe';
import { supabase } from '@/lib/supabase-server'; // 服务端Supabase客户端
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const { priceId, userId } = await request.json();

  // 从数据库获取用户的Stripe Customer ID，如果没有则创建一个
  const { data: user } = await supabase.from('users').select('stripe_customer_id').eq('id', userId).single();
  
  let customerId = user?.stripe_customer_id;
  if (!customerId) {
    const customer = await stripe.customers.create({ email: user.email });
    customerId = customer.id;
    await supabase.from('users').update({ stripe_customer_id: customerId }).eq('id', userId);
  }

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    mode: 'subscription',
    customer: customerId,
    line_items: [{ price: priceId, quantity: 1 }],
    success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?success=true`,
    cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing`,
  });

  return NextResponse.json({ sessionId: session.id });
}
```

#### b. 前端: 调用支付接口

在你的定价页面组件中，添加一个按钮，点击后调用此API并跳转到Stripe托管的支付页面。

### 3.3 处理 Webhooks

当订阅状态发生变化时（如支付成功、续订失败），Stripe 会通过 Webhook 通知你的应用。这是更新数据库中用户订阅状态的关键。

1.  **创建 Webhook 端点**:
    ```typescript
    // website/src/app/api/webhooks/stripe/route.ts
    import { stripe } from '@/lib/stripe';
    import { headers } from 'next/headers';
    import Stripe from 'stripe';

    export async function POST(req: Request) {
      const body = await req.text();
      const signature = headers().get('Stripe-Signature') as string;
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

      let event: Stripe.Event;
      try {
        event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      } catch (err: any) {
        return new Response(`Webhook Error: ${err.message}`, { status: 400 });
      }

      // 根据不同的事件类型，处理业务逻辑
      switch (event.type) {
        case 'checkout.session.completed':
          // 支付成功，更新数据库中的用户订阅状态
          // ...
          break;
        case 'customer.subscription.updated':
          // 订阅更新，例如升级或降级
          // ...
          break;
        // ...处理其他事件
      }

      return new Response(null, { status: 200 });
    }
    ```
2.  **在 Stripe 中配置 Webhook**:
    -   在 Stripe 仪表盘的 "Developers" -> "Webhooks" 中，添加一个新的端点。
    -   URL填你的生产环境Webhook地址：`https://your-domain.com/api/webhooks/stripe`。
    -   选择需要监听的事件。

3.  **本地测试 Webhook**:
    使用 [Stripe CLI](https://stripe.com/docs/stripe-cli) 可以在本地测试Webhook，而无需部署。
    ```bash
    # 安装Stripe CLI并登录
    stripe login

    # 将Stripe事件转发到你的本地开发服务器
    stripe listen --forward-to localhost:3000/api/webhooks/stripe
    ```
