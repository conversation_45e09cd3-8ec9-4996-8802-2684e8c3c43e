# 4.2 Chrome插件开发指南

## 1. 开发流程

典型的插件功能开发流程如下：

1.  **启动开发服务器**:
    ```bash
    # 在项目根目录运行
    npm run dev:extension
    ```
    Vite 会监听 `extension/src` 目录下的文件变动，并自动重新构建到 `extension/dist` 目录，实现热重载。

2.  **加载开发版插件**:
    -   打开 Chrome 浏览器，进入 `chrome://extensions` 页面
    -   开启右上角的 **"开发者模式"** 开关
    -   点击左上角的 **"加载已解压的扩展程序"** 按钮
    -   选择项目中的 `extension/dist` 目录
    -   成功后可以看到 EchoSync 插件卡片出现在扩展列表中
    -   每次修改完代码后，需要点击插件卡片上的 **"刷新"** 按钮来重新加载最新的更改

3.  **修改代码**: 在 `extension/src` 中进行开发。例如，要修改 Popup 界面，可以编辑 `extension/src/popup/` 下的文件。

4.  **查看效果**:
    -   如果修改的是 Popup 或 Options 页面，通常刷新页面或重新打开弹窗即可看到变化。
    -   如果修改的是 Content Script，需要刷新注入该脚本的AI聊天网页。
    -   如果修改的是 Background Service Worker，通常需要点击 `chrome://extensions` 页面中插件卡片上的“刷新”按钮来重启 Service Worker。

## 2. 核心模块调试技巧

### 2.1 Popup 调试

-   在浏览器工具栏 **右键点击** EchoSync 图标。
-   选择 **“审查弹出内容”** (Inspect Popup)。
-   这会打开一个独立的开发者工具窗口，你可以在此检查元素、查看控制台日志、设置断点，就像调试普通网页一样。

### 2.2 Background Service Worker 调试

-   打开 `chrome://extensions` 页面。
-   找到 EchoSync 插件卡片。
-   点击卡片上的 **“服务工作线程”** (Service Worker) 链接。
-   这会打开一个专门的开发者工具窗口，用于调试后台脚本。所有在 `background/index.ts` 中的 `console.log` 输出都会显示在这里。

### 2.3 Content Script 调试

-   打开一个插件支持的AI聊天网站 (如 `https://chat.openai.com`)。
-   在页面上 **右键点击**，选择 **“检查”** (Inspect) 打开常规的开发者工具。
-   **关键一步**: 在 `Console` (控制台) 标签页的顶部，有一个下拉框（通常显示为 `top`）。点击它，并切换到 **“EchoSync - AI提示词同步器”** 的沙箱环境。
-   切换后，你就可以看到所有来自 Content Script 的日志，并可以在此环境中执行代码与Content Script交互。
-   在 `Sources` (源代码) 标签页，你可以在 `Content scripts` 分组下找到插件的源文件并设置断点。

## 3. 添加对新AI平台的支持

这是本插件最常见的扩展需求之一， благодаря 适配器模式，这个过程非常简单：

1.  **创建新的适配器文件**:
    在 `extension/src/content/adapters/` 目录下，仿照 `chatgpt.ts` 创建一个新文件，例如 `newplatform.ts`。

2.  **实现��配器类**:
    ```typescript
    // extension/src/content/adapters/newplatform.ts
    import { AIAdapter } from './base';

    export class NewPlatformAdapter extends AIAdapter {
      platformName = 'NewPlatform';
      
      // 关键：找到新平台对应的CSS选择器
      selectors = {
        inputField: 'textarea.new-platform-input',
        sendButton: 'button.new-platform-send',
        messageContainer: 'div.chat-history'
      };

      // 实现基类的其他抽象方法...
      async injectPrompt(prompt: string): Promise<void> {
        // ...
      }
      // ...
    }
    ```

3.  **注册适配器**:
    在 `extension/src/content/index.ts` 中，导入并注册新的适配器。

4.  **更新 `manifest.json`**:
    将新平台的域名添加到 `host_permissions` 数组中，以获得在该网站上运行脚本的权限。
    ```json
    "host_permissions": [
      "https://chat.openai.com/*",
      "https://newplatform.com/*" // 新增权限
    ]
    ```

5.  重新加载插件并测试。
