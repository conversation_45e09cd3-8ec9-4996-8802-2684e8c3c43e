# 5.2 CI/CD 与部署

为了实现快速、可靠的软件交付，项目配置了基于 GitHub Actions 的 CI/CD (持续集成/持续部署) 流水线，并为插件和网站选择了合适的部署策略。

## 1. CI/CD 流水线 (GitHub Actions)

CI/CD 流程定义在 `.github/workflows/ci.yml` 文件中。

### 触发条件

-   当有代码 `push` 到 `main` 或 `develop` 分支时。
-   当有 `pull_request` 提交到 `main` 分支时。

### 核心流程

当流水线被触发后，会自动执行以下任务：

1.  **Checkout Code**: 拉取最新的代码。
2.  **Setup Environment**: 设置 Node.js 运行环境。
3.  **Install Dependencies**: 执行 `npm install` 安装所有依赖。
4.  **Lint**: 执行 `npm run lint` 检查代码风格是否符合规范。
5.  **Test**: 执行 `npm run test` 运行所有单元测试和组件测试。
6.  **Build**: 执行 `npm run build` 构建 Chrome 插件和官方网站的生产版本。
7.  **Upload Artifacts**: 将构建产物（如 `extension/dist` 目录）打包并上传，以供后续部署或手动下载。

这个流程确保了所有进入主分支的代码都经过了自动化的质量检查，有效防止了低级错误和构建失败的问题。

## 2. Chrome 插件部署

Chrome 插件需要手动上传到 **Chrome网上应用店开发者信息中心**。

### 部署步骤

1.  **构建生产版本**:
    ```bash
    # 在项目根目录运行
    npm run build:extension
    ```

2.  **打包插件**:
    进入 `extension/dist` 目录，将其所有内容压缩成一个 `.zip` 文件。例如，`echosync-v1.0.0.zip`。

3.  **上传到商店**:
    -   访问 [Chrome网上应用店开发者信息中心](https://chrome.google.com/webstore/devconsole)。
    -   选择你的插件项目。
    -   在“软件包”标签页下，上传刚刚创建的 `.zip` 文件。
    -   填写更新说明、宣传图等信息。
    -   提交审核。

## 3. 官方网站部署

官方网站推荐使用 **Vercel** 进行部署，因为它与 Next.js 无缝集成，提供全球CDN和零配置体验。

### 部署策略

-   **生产环境**: `main` 分支的任何更新都会被 Vercel 自动拉取、构建并部署到生产域名（如 `echosync.ai`）。
-   **预览环境**: 每个 `pull_request` 都会被 Vercel 自动部署到一个唯一的预览URL。这使得在合并代码前，可以方便地在真实环境中预览和测试新功能。

### 部署步骤

1.  **关联 Vercel**:
    -   使用你的 GitHub 账户登录 Vercel。
    -   导入你的 EchoSync Git 仓库。
    -   Vercel 会自动识别出这是一个 Next.js 项目，并配置好构建命令 (`npm run build:website`) 和输出目录。

2.  **配置环境变量**:
    在 Vercel 项目的设置中，将你在 `.env.local` 中使用的所有环境变量（如 Supabase 和 Stripe 的密钥）配置上去。这是保证生产环境能正常连接后端服务的关键。

3.  **触发部署**:
    之后，每当你向 `main` 分支推送代码时，Vercel 都会自动完成整个部署流程。

### 备选方案：Cloudflare

-   **优势**: 可能提供更低的成本、强大的边缘计算能力（Workers）和D1数据库集成。
-   **适用场景**: 当项目发展到一定规模，对成本控制和全球性能有更高要求时，可以考虑从 Vercel 迁移到 Cloudflare。初期建议使用 Vercel 以求快速上线。
