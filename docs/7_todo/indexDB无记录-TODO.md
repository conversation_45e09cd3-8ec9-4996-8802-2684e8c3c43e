# 修复提示词存储问题 TODO

## 问题分析
当前问题：deepseek输入提示词发送成功后，IndexedDB并未有记录，原因是useChatHistory的createChat方法并未被调用，background脚本还在使用原始的storage存储prompt。

## 任务列表

### 🎯 任务1: 修复background脚本中的提示词存储逻辑 [P0]
- [x] 1.1 修改background/index.ts中的handleSyncPrompt函数
  - 移除StorageService.addPrompt调用
  - 改为使用chatHistoryService.create方法
  - 确保正确传递平台信息和提示词内容
  - 添加平台名称标准化逻辑
  - 修复handleCapturePrompt函数
  - 更新GET_HISTORY消息处理
  - 修复定期清理逻辑

### 🎯 任务2: 修复content脚本中的提示词捕获逻辑 [P0]
- [x] 2.1 检查content/index.ts中的提示词捕获流程
  - 确保发送到background的消息格式正确
  - 验证平台识别逻辑
  - 消息格式已正确，无需修改

### 🎯 任务3: 清理冗余的storage代码 [P1]
- [x] 3.1 清理storage.ts中关于提示词的方法
  - 移除getPrompts()方法
  - 移除savePrompts()方法
  - 移除addPrompt()方法
  - 保留其他非提示词相关的方法
  - 更新导出/导入数据方法

- [x] 3.2 确认useStorage.ts无需修改
  - 该文件主要处理数据库连接管理，无提示词相关逻辑

### 🎯 任务4: 更新content脚本中的存储调用 [P1]
- [x] 4.1 检查并更新所有直接调用chrome.storage的提示词相关代码
  - 替换为使用chatHistoryService
  - 确保平台信息正确传递
  - 更新app-store.ts中的提示词方法
  - 修复Claude适配器编译错误

### 🎯 任务5: 测试验证 [P0]
- [ ] 5.1 测试deepseek平台提示词存储
- [ ] 5.2 测试其他平台提示词存储
- [ ] 5.3 验证IndexedDB中的数据正确性
- [ ] 5.4 测试提示词历史显示功能

## 实施顺序
1. 先修复background脚本的存储逻辑（任务1）
2. 验证content脚本的消息发送（任务2）
3. 清理冗余代码（任务3-4）
4. 全面测试（任务5）

## 注意事项
- 确保平台ID正确传递和识别
- 保持chat_uid的生成和共享逻辑不变
- 不要破坏现有的UI组件和历史显示功能
