# Background操作数据库重构 - TODO

## 需求分析
1. **重构background/index.ts**: 按照维护生命周期、消息处理、数据库通信拆分为多个模块
2. **修复数据库初始化**: background需要负责dexie的开启，但目前日志显示数据库未正确初始化
3. **清理冗余hooks**: hooks目录中的useHook设计现在用不到了，需要清理

## 任务分解

### 🎯 任务1: 重构background架构 [P0]

#### 1.1 创建生命周期管理模块
- [ ] 创建 `background/lifecycle.ts`
  - 处理扩展安装、更新、启动事件
  - 管理Service Worker生命周期
  - 初始化数据库连接

#### 1.2 创建消息处理模块  
- [ ] 创建 `background/messageHandler.ts`
  - 统一处理所有消息类型
  - 路由消息到对应的处理器
  - 错误处理和响应格式化

#### 1.3 创建数据库管理模块
- [ ] 创建 `background/databaseManager.ts`
  - 负责dexie数据库的初始化和连接
  - 提供数据库操作的统一接口
  - 处理数据库错误和重连

#### 1.4 重构主入口文件
- [ ] 重构 `background/index.ts`
  - 简化为模块组装和启动
  - 清晰的模块依赖关系
  - 统一的日志和错误处理

### 🎯 任务2: 修复数据库初始化问题 [P0]

#### 2.1 诊断当前问题
- [ ] 检查dexie数据库初始化代码
- [ ] 确认Service Worker中的数据库连接状态
- [ ] 分析为什么数据库初始化日志缺失

#### 2.2 修复初始化流程
- [ ] 确保在Service Worker启动时正确初始化数据库
- [ ] 添加数据库连接成功/失败的日志
- [ ] 实现数据库连接重试机制

#### 2.3 验证数据库操作
- [ ] 测试所有数据库CRUD操作
- [ ] 确认消息处理中的数据库调用正常
- [ ] 验证数据持久化功能

### 🎯 任务3: 清理冗余hooks [P1]

#### 3.1 分析hooks使用情况
- [ ] 检查哪些hooks文件已不再使用
- [ ] 确认没有其他模块依赖这些hooks
- [ ] 列出需要删除的文件清单

#### 3.2 清理hooks目录
- [ ] 删除不再使用的useHook文件
- [ ] 更新相关的导入引用
- [ ] 清理package.json中的相关依赖

### 🎯 任务4: 完善日志和监控 [P1]

#### 4.1 添加结构化日志
- [ ] 实现统一的日志格式
- [ ] 添加不同级别的日志输出
- [ ] 包含模块名称和操作类型

#### 4.2 添加性能监控
- [ ] 监控数据库操作耗时
- [ ] 监控消息处理性能
- [ ] 添加错误统计和报告

## 文件结构设计

```
extension/src/background/
├── index.ts              # 主入口，模块组装
├── lifecycle.ts          # 生命周期管理
├── messageHandler.ts     # 消息处理路由
├── databaseManager.ts    # 数据库管理
├── handlers/            # 具体消息处理器
│   ├── chatHistoryHandler.ts
│   ├── platformHandler.ts
│   └── syncHandler.ts
└── utils/
    ├── logger.ts        # 日志工具
    └── errorHandler.ts  # 错误处理
```

## 实施顺序
1. **诊断数据库问题** (任务2.1) - 找出根本原因
2. **创建数据库管理模块** (任务1.3) - 修复初始化
3. **重构消息处理** (任务1.2) - 改善架构
4. **完善生命周期管理** (任务1.1) - 统一管理
5. **清理冗余代码** (任务3) - 代码整理
6. **完善监控日志** (任务4) - 提升可维护性

## 验收标准

### 功能验收
- [ ] Service Worker启动时显示完整的初始化日志
- [ ] 数据库连接成功并可正常CRUD操作
- [ ] 所有消息类型都能正确处理和响应
- [ ] 错误情况有清晰的日志和处理

### 架构验收  
- [ ] 代码按功能模块清晰分离
- [ ] 模块间依赖关系明确
- [ ] 易于测试和维护
- [ ] 符合单一职责原则

### 性能验收
- [ ] 数据库操作响应时间 < 100ms
- [ ] 消息处理延迟 < 50ms
- [ ] Service Worker内存占用合理
- [ ] 无内存泄漏问题

## 风险评估
- **数据丢失风险**: 重构过程中确保数据库迁移安全
- **功能回归风险**: 充分测试所有现有功能
- **性能影响**: 监控重构后的性能表现