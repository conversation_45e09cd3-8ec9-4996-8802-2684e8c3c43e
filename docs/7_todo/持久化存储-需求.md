# 需求

* 新增useHook封装存储模块，存储不使用chrome的storage，使用  代替sqlite3，参考`docs/8_storage/使用Dexie.js + IndexedDB.md`文档中的实现
* 请帮我设计存储的表结构，首先是`chat_history`表，包含如下字段：
  * id: 主键，自增
  * chat_prompt: 聊天页面输入框的提示词
  * chat_answer: 聊天页面输入提示词对应的回答
  * chat_uid: 聊天的唯一id，暂时使用秒级别时间戳
  * platform_id: 平台，比如是deepseek还是chatgpt等等
  * tags: 标签，数组类型 暂时为空
  * chat_group_name：聊天组名称，一般位于对话的顶部
  * chat_sort: 有的平台，比如像deeepseek可以获得本次问题在本对话的序号
  * p_uid: 从当前平台获得的，本次文词在当前聊天平台的唯一id，有的是session_id+序号
  * create_time 创建时间 取时间戳
  * is_synced: 是否已同步过，0否1是，默认0
  * is_delete: 是否删除，0否1是，默认0
* 设计表`platform` 作为不同平台的字典表，存储如下字段。
  * id 主键，自增
  * name: 平台名称，比如deepseek
  * url: 平台的url，比如https://chat.deepseek.com
  * icon: 平台的faviconl路径,表示网站的图标
  * is_delete: 是否删除，0否1是，默认0
  
表对应的typescript类型存储在types目录下
使用useHook的存储接口设计，未来可切换其他版本，比如sqlit-wasm的持久层。
* 鼠标悬浮到小球上后，小球上方出现一堆聊天气泡，为从`chat_history`历史提示词记录`chat_prompt`,按照chat_uid去重，按照create_time升序排列，即离小球最近为时间最近
* 提示词旁边显示已经在对应网站，发送过提示词的网站icon
