# 2.1 项目整体技术栈

## Monorepo 技术栈

项目采用 Monorepo 架构进行管理，统一了代码规范、构建流程和依赖管理。

| 技术 | 版本 | 用途 |
|---|---|---|
| **npm Workspaces** | - | 管理 `extension` 和 `website` 两个包 |
| **ESLint** | 8.45.0+ | 统一的代码规范检查，支持TypeScript和React |
| **Prettier** | 3.1.1+ | 统一的代码格式化，配合Tailwind CSS插件 |
| **TypeScript** | 5.8.3+ | 全项目范围的类型安全，严格模式配置 |
| **GitHub Actions** | v4 | CI/CD (持续集成/持续部署)，自动化测试和构建 |
| **Concurrently** | 8.2.2 | 同时运行多个开发服务器 |

---

## 🔌 Chrome插件技术栈

### 核心框架与构建

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **核心框架** | React + TypeScript | 18.2.0 / 5.8.3 | 组件化开发，类型安全，提升代码质量和可维护性 |
| **构建工具** | Vite + @crxjs/vite-plugin | 5.0.0 / 2.0.2 | 极速热更新和构建，专为Chrome插件（MV3）优化的自动化打包 |
| **包管理** | npm | - | 依赖管理和脚本执行 |
| **模块系统** | ES Modules | - | 现代化的模块系统，支持Tree Shaking |

### UI与样式系统

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **样式系统** | Tailwind CSS | 3.3.3 | 原子化CSS，提供极高的开发效率和统一的设计语言 |
| **UI组件库** | shadcn/ui (基于 Radix UI) | 1.3.0+ | 轻量、高可定制、现代美观，完美适配插件的小窗口UI |
| **图标库** | Lucide React | 0.263.1 | 现代化的图标库，与设计系统完美集成 |
| **动画库** | tailwindcss-animate | 1.0.7 | 基于Tailwind的动画工具类 |
| **样式工具** | class-variance-authority | 0.7.0 | 类型安全的样式变体管理 |
| **样式合并** | tailwind-merge + clsx | 1.14.0 / 2.0.0 | 智能的样式类合并和条件样式 |

### 状态管理与路由

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **状态管理** | Zustand | 4.4.0 | 简洁、轻量、无模板代码，非常适合插件的弹窗和后台状态通信 |
| **路由管理** | React Router DOM | 6.8.0 | 用于实现插件内部页面（如Popup和Options页面）的导航 |
| **数据库** | Dexie (IndexedDB) | 4.0.11 | 客户端数据库，用于本地数据存储和缓存 |
| **工具库** | date-fns + nanoid | 2.30.0 / 4.0.2 | 日期处理和唯一ID生成 |

### 开发工具与测试

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **测试框架** | Jest + React Testing Library | 29.6.4 / 13.4.0 | 提供完整的单元测试和组件测试覆盖，保证代码质量 |
| **测试环境** | jest-environment-jsdom | 29.6.4 | 模拟浏览器环境进行DOM测试 |
| **TypeScript编译** | ts-jest + ts-node | 29.1.1 / 10.9.1 | TypeScript测试支持 |
| **代码检查** | @typescript-eslint | 6.21.0 | TypeScript专用的ESLint规则 |
| **构建优化** | @rollup/plugin-typescript | 12.1.4 | TypeScript构建优化 |

---

## 🌐 官方网站技术栈

### 核心框架与后端

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **全栈框架** | Next.js (App Router) | 14.0.4 | 业界领先的React框架，支持SSR/SSG，提供优秀的性能和开发体验 |
| **后端服务** | Supabase | 2.38.4+ | 提供数据库（PostgreSQL）、用户认证、存储等一体化后端服务，简化开发 |
| **认证系统** | Supabase Auth | 0.8.7+ | 完整的用户认证解决方案，支持多种登录方式 |
| **支付系统** | Stripe | 14.9.0+ | 全球领先的在线支付解决方案，用于处理用户订阅和计费 |
| **部署平台** | Vercel | - | 与Next.js无缝集成，提供零配置、全球CDN加速的自动化部署 |

### UI与交互

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **样式系统** | Tailwind CSS + shadcn/ui | 3.3.6 / 1.0.0+ | 与Chrome插件保持设计风格的完全统一 |
| **UI组件库** | Radix UI | 1.0.0+ | 无障碍、可定制的原始UI组件 |
| **动画库** | Framer Motion | 10.16.16 | 为网站添加流畅、现代的交互动画，提升用户体验 |
| **表单处理** | React Hook Form + Zod | 7.48.2 / 3.22.4 | 高性能表单处理和数据验证 |
| **图表库** | Recharts | 2.8.0 | React图表库，用于数据可视化 |
| **Markdown** | react-markdown + remark-gfm | 9.0.1 / 4.0.0 | Markdown内容渲染支持 |

### 开发工具

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **开发工具** | tsx + supabase CLI | 4.6.2 / 1.123.4 | TypeScript执行和数据库管理 |
| **代码格式化** | Prettier + Tailwind插件 | 3.1.1 / 0.5.9 | 代码格式化和Tailwind类排序 |
| **测试框架** | Jest + Testing Library | 29.7.0 / 13.4.0 | 网站功能测试 |

---

## 🏗️ 架构特性与配置

### Chrome插件架构

| 特性 | 实现方案 | 说明 |
|---|---|---|
| **Manifest版本** | Manifest V3 | 最新的Chrome插件标准，更安全和高效 |
| **Service Worker** | Background Script | 替代传统的Background Page，更节能 |
| **Content Scripts** | 多平台适配器 | 支持ChatGPT、Claude、Gemini、DeepSeek、Kimi等平台 |
| **热重载** | Vite HMR + @crxjs | 开发时支持热重载，提升开发效率 |
| **权限管理** | 最小权限原则 | 仅申请必要的权限，保护用户隐私 |
| **跨域支持** | Host Permissions | 支持多个AI平台的跨域访问 |

### 数据存储方案

| 存储类型 | 技术选型 | 用途 |
|---|---|---|
| **插件本地存储** | Chrome Storage API + Dexie | 用户设置、聊天历史、平台配置 |
| **网站数据库** | Supabase PostgreSQL | 用户账户、订阅信息、云端同步数据 |
| **缓存策略** | IndexedDB | 离线数据缓存和快速访问 |
| **文件存储** | Supabase Storage | 用户头像、附件等文件资源 |

### 开发与部署流程

| 阶段 | 工具/平台 | 配置 |
|---|---|---|
| **开发环境** | Vite Dev Server | 支持热重载，端口5173 |
| **代码质量** | ESLint + Prettier + TypeScript | 严格的代码规范和类型检查 |
| **测试覆盖** | Jest + React Testing Library | 单元测试和组件测试 |
| **构建优化** | Vite Production Build | 代码分割、压缩、Tree Shaking |
| **CI/CD** | GitHub Actions | 自动化测试、构建和部署 |
| **插件发布** | Chrome Web Store | 手动上传和审核 |
| **网站部署** | Vercel | 自动化部署，全球CDN |

### 性能优化策略

| 优化方向 | 实现方案 | 效果 |
|---|---|---|
| **包体积优化** | Tree Shaking + 代码分割 | 减少插件体积，提升加载速度 |
| **运行时性能** | React.memo + useMemo | 减少不必要的重渲染 |
| **数据库查询** | 索引优化 + 分页加载 | 提升数据查询效率 |
| **网络请求** | 请求缓存 + 批量操作 | 减少网络延迟 |
| **用户体验** | 骨架屏 + 加载状态 | 提升感知性能 |

### 安全性保障

| 安全层面 | 实现方案 | 说明 |
|---|---|---|
| **数据传输** | HTTPS + CSP | 加密传输和内容安全策略 |
| **用户认证** | Supabase Auth + JWT | 安全的用户身份验证 |
| **权限控制** | 最小权限原则 | 仅申请必要的浏览器权限 |
| **数据隐私** | 本地优先存储 | 敏感数据优先本地存储 |
| **代码安全** | TypeScript + ESLint | 类型安全和代码规范检查 |
