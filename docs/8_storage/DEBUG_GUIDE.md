# EchoSync 插件调试指南

## 问题描述
在 https://www.kimi.com/chat/d1qgdmrfj2jc9eke0380 页面没有出现悬浮小球

## 调试步骤

### 1. 检查插件是否已安装并启用
1. 打开 Chrome 扩展管理页面：`chrome://extensions/`
2. 确认 "EchoSync - AI提示词同步器" 已安装并启用
3. 确认开发者模式已开启

### 2. 检查控制台日志
1. 在 Kimi 页面按 F12 打开开发者工具
2. 切换到 Console 标签
3. 刷新页面，查看是否有以下日志：
   ```
   EchoSync Content Script loaded on: https://www.kimi.com/chat/...
   Current hostname: www.kimi.com
   Detecting platform for hostname: www.kimi.com
   Checking Kimi domains...
   hostname.includes("kimi.com"): true
   Detected Kimi platform
   KimiAdapter constructor called
   KimiAdapter initialized with selectors: {...}
   Starting initUniversalFeatures for Kimi
   Page load completed
   Checking if page is valid for Kimi...
   KimiAdapter.isValidPage() - hostname: www.kimi.com isValid: true
   Page validity check result: true
   Initializing universal features for Kimi...
   Creating floating bubble for platform: Kimi
   Floating bubble created and added to DOM: <div>
   Universal features initialized for Kimi
   ```

### 3. 如果没有看到日志
可能的原因：
- Content script 没有注入
- 页面 URL 不匹配 manifest.json 中的规则
- 插件权限问题

### 4. 检查 DOM 元素
在控制台中运行：
```javascript
// 检查是否有悬浮小球元素
document.getElementById('echosync-floating-bubble')

// 检查所有 EchoSync 相关元素
document.querySelectorAll('[id*="echosync"]')

// 手动触发平台检测
console.log('Current hostname:', window.location.hostname)
console.log('Includes kimi.com:', window.location.hostname.includes('kimi.com'))
```

### 5. 手动测试
在控制台中运行以下代码来手动创建悬浮小球：
```javascript
// 创建测试悬浮小球
const testBubble = document.createElement('div');
testBubble.id = 'test-floating-bubble';
testBubble.innerHTML = '<div style="color: white; font-size: 12px;">TEST</div>';
testBubble.style.cssText = `
  position: fixed;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;
document.body.appendChild(testBubble);
console.log('Test bubble created');
```

### 6. 检查插件权限
确认插件在 Kimi 网站上有权限：
1. 点击地址栏右侧的插件图标
2. 确认 EchoSync 插件显示并可以访问当前网站

### 7. 重新加载插件
如果以上都没问题，尝试：
1. 在 `chrome://extensions/` 页面点击 EchoSync 插件的"重新加载"按钮
2. 刷新 Kimi 页面

## 常见问题解决方案

### 问题1：Content Script 没有注入
- 检查 manifest.json 中的 matches 规则是否包含当前 URL
- 确认插件有足够的权限

### 问题2：平台检测失败
- 检查 hostname 是否正确
- 确认 KimiAdapter 的 isValidPage() 方法返回 true

### 问题3：DOM 元素创建失败
- 检查是否有 JavaScript 错误
- 确认页面完全加载后再创建元素

## 联系支持
如果问题仍然存在，请提供：
1. 控制台的完整日志
2. 插件版本信息
3. Chrome 版本信息
4. 具体的页面 URL
