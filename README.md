# EchoSync - AI提示词同步器

<div align="center">
  <img src="./assets/logo.png" alt="EchoSync Logo" width="200" />
  <h1>EchoSync</h1>
  <p>跨平台AI提示词同步与管理工具</p>
</div>

## 🌟 功能特性

- 🔄 **跨平台同步**: 在一个AI平台输入，自动同步到其他平台
- 📚 **提示词管理**: 集中存储、分类和搜索您的AI提示词
- 📱 **多设备支持**: 通过云端同步，在任何设备上访问您的提示词
- 🔍 **智能搜索**: 快速找到历史提示词和对话
- 🔒 **安全可靠**: 本地优先存储，保护您的数据隐私

## 🚀 快速开始

想要快速体验EchoSync？请查看我们的[快速开始指南](docs/QUICK_START.md)，其中包含详细的安装步骤、使用方法和常见问题解答。

## 📚 文档导航

| 文档名称 | 描述 | 链接 |
|---------|------|------|
| 快速开始指南 | 项目环境设置和基本使用步骤 | [查看文档](docs/QUICK_START.md) |
| 项目结构说明 | 详细的项目目录和文件结构 | [查看文档](docs/PROJECT_STRUCTURE.md) |
| 开发指南 | 完整的开发流程和最佳实践 | [查看文档](docs/DEVELOPMENT_GUIDE.md) |
| 项目总结 | 项目概述、进度和未来规划 | [查看文档](docs/PROJECT_SUMMARY.md) |
| 项目总览 | 0：项目总览 | [查看文档](docs/0：项目总览) |
| 项目规划 | 1：规划 | [查看文档](docs/1：规划.md) |
| 技术栈 | 2：技术栈 | [查看文档](docs/2：技术栈.md) |
| 客户端技术选型 | 3：户端技术选型 | [查看文档](docs/3：户端技术选型.md) |
| Chrome插件开发计划 | 4：Chrome插件开发计划 | [查看文档](docs/4：Chrome插件开发计划.md) |
| 项目总结 | 5：总结 | [查看文档](docs/5：总结) |
| 项目设计 | 6：使用指南 | [查看文档](docs/6：使用指南.md) |
| 项目进度 | 7：设计详情 | [查看文档](docs/7：设计详情.md) |
| 项目总结 | 8：项目状态和路线图 | [查看文档](docs/8：项目状态和路线图.md) |

## 📊 项目状态

当前版本: `v0.1.0-alpha (开发中)`

有关项目完成度、开发进度和未来规划的详细信息，请查看[项目总结](docs/PROJECT_SUMMARY.md)。

## 📁 项目结构

本项目采用Monorepo结构，包含Chrome插件和官方网站两个主要部分。

有关项目结构的详细说明，请查看[项目结构总览](docs/PROJECT_STRUCTURE.md)。

## 🔧 开发指南

### 📋 环境要求
- **Node.js**: 18.0+ (推荐使用 LTS 版本)
- **包管理器**: npm 9+ 或 yarn 1.22+
- **浏览器**: Chrome 88+ (支持MV3)
- **编辑器**: VS Code (推荐安装相关插件)

### 🚀 快速开始

#### 1. 项目初始化
```bash
# 克隆项目
git clone https://github.com/your-username/echosync.git
cd echosync

# 安装所有依赖 (Monorepo)
npm install

# 或使用便捷脚本
npm run setup
```

#### 2. 开发环境启动
```bash
# 同时启动插件和网站开发服务器
npm run dev

# 或分别启动
npm run dev:extension  # 插件开发 (http://localhost:5173)
npm run dev:website    # 网站开发 (http://localhost:3000)
```

#### 3. Chrome插件加载
```bash
# 构建插件
cd extension && npm run build

# Chrome浏览器操作:
# 1. 打开 chrome://extensions/
# 2. 开启"开发者模式"
# 3. 点击"加载已解压的扩展程序"
# 4. 选择 extension/dist 目录
```

### 🏗️ 开发工作流

#### Git工作流
```bash
# 1. 创建功能分支
git checkout -b feature/your-feature-name

# 2. 开发和提交 (遵循 Conventional Commits)
git add .
git commit -m "feat: add prompt sync functionality"

# 3. 推送和创建PR
git push origin feature/your-feature-name
```

#### 代码规范
- **ESLint + Prettier**: 自动格式化和代码检查
- **TypeScript严格模式**: 类型安全保证
- **测试覆盖率**: 新功能需要单元测试
- **组件文档**: 复杂组件需要JSDoc注释


---

<div align="center">
  <h3>🌟 如果这个项目对你有帮助，请给我们一个 Star ⭐️</h3>

  <p>
    <a href="https://github.com/your-username/echosync/stargazers">
      <img src="https://img.shields.io/github/stars/your-username/echosync?style=social" alt="GitHub stars">
    </a>
    <a href="https://github.com/your-username/echosync/network/members">
      <img src="https://img.shields.io/github/forks/your-username/echosync?style=social" alt="GitHub forks">
    </a>
    <a href="https://twitter.com/intent/tweet?text=Check%20out%20EchoSync%20-%20AI%20Prompt%20Sync%20Tool&url=https://github.com/your-username/echosync">
      <img src="https://img.shields.io/twitter/url?style=social&url=https://github.com/your-username/echosync" alt="Tweet">
    </a>
  </p>

  <p><strong>Made with ❤️ by EchoSync Team</strong></p>
  <p><em>让AI提示词管理更简单，让创作更高效</em></p>
</div>